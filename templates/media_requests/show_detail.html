{% extends 'base.html' %}

{% block title %}{{ tv_show.title }} - Overseerr Dashboard{% endblock %}

{% block extra_css %}
<style>
    /* Toast styling */
    .toast {
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .toast-header.bg-success-subtle {
        border-bottom: 1px solid rgba(25, 135, 84, 0.2);
    }

    .toast-header.bg-danger-subtle {
        border-bottom: 1px solid rgba(220, 53, 69, 0.2);
    }

    .toast-header.bg-warning-subtle {
        border-bottom: 1px solid rgba(255, 193, 7, 0.2);
    }

    .toast-header.bg-info-subtle {
        border-bottom: 1px solid rgba(13, 202, 240, 0.2);
    }

    /* Spinner animation for search buttons */
    .auto-search-btn:disabled {
        opacity: 0.6;
    }

    /* Episode row styling */
    .episode-row.available {
        background-color: rgba(25, 135, 84, 0.1);
    }

    .episode-row.unavailable {
        background-color: rgba(220, 53, 69, 0.05);
    }

    .quality-badge {
        font-size: 0.75em;
    }

    /* Download status button styles */
    .btn-danger {
        font-weight: 600;
    }

    .btn-danger:hover {
        transform: scale(1.05);
        transition: transform 0.2s ease;
    }

    /* Status indicator animations */
    .btn-warning i {
        animation: pulse 2s infinite;
    }

    .btn-info i {
        animation: bounce 1s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-3px); }
        60% { transform: translateY(-1px); }
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .auto-search-btn.searching i {
        animation: spin 1s linear infinite;
    }
</style>
{% endblock %}

{% block content %}
<!-- Show Header -->
<div class="row mb-4">
    <div class="col-md-3">
        {% if tv_show.poster_path %}
            <img src="https://image.tmdb.org/t/p/w500{{ tv_show.poster_path }}"
                 class="img-fluid rounded shadow" alt="{{ tv_show.title }}">
        {% else %}
            <div class="bg-light rounded shadow d-flex align-items-center justify-content-center"
                 style="height: 450px;">
                <i class="bi bi-tv text-muted" style="font-size: 4rem;"></i>
            </div>
        {% endif %}
    </div>
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <h1 class="display-5">{{ tv_show.title }}</h1>
            <div class="d-flex align-items-center gap-2">
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-sliders me-1"></i>
                        Profile: {{ tv_show.quality_profile.name|default:"None" }}
                    </button>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">Quality Profiles</h6></li>
                        <li><a class="dropdown-item {% if not tv_show.quality_profile %}active{% endif %}"
                               href="#" onclick="assignProfile(null)">
                            <i class="bi bi-x-circle me-1"></i>No Profile
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <!-- We'll populate this with JavaScript -->
                        <div id="profileDropdownItems"></div>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/profiles/" target="_blank">
                            <i class="bi bi-gear me-1"></i>Manage Profiles
                        </a></li>
                    </ul>
                </div>

            </div>
        </div>

        <!-- Show Info -->
        <div class="row mb-3">
            <div class="col-md-6">
                {% if tv_show.first_air_date %}
                    <p class="mb-1">
                        <strong><i class="bi bi-calendar me-1"></i>First Aired:</strong>
                        {{ tv_show.first_air_date|date:"F d, Y" }}
                    </p>
                {% endif %}
                {% if tv_show.last_air_date %}
                    <p class="mb-1">
                        <strong><i class="bi bi-calendar-check me-1"></i>Last Aired:</strong>
                        {{ tv_show.last_air_date|date:"F d, Y" }}
                    </p>
                {% endif %}
                {% if tv_show.status %}
                    <p class="mb-1">
                        <strong><i class="bi bi-info-circle me-1"></i>Status:</strong>
                        {{ tv_show.status }}
                    </p>
                {% endif %}
            </div>
            <div class="col-md-6">
                <p class="mb-1">
                    <strong><i class="bi bi-collection me-1"></i>Seasons:</strong>
                    {{ tv_show.get_available_seasons }}/{{ tv_show.get_total_seasons }} available
                </p>
                <p class="mb-1">
                    <strong><i class="bi bi-play me-1"></i>Episodes:</strong>
                    {{ tv_show.get_available_episodes }}/{{ tv_show.get_total_episodes }} available
                </p>
                {% if tv_show.added_by or tv_show.added_at %}
                    {% if tv_show.added_by %}
                        <p class="mb-1">
                            <strong><i class="bi bi-person me-1"></i>Added by:</strong>
                            {{ tv_show.added_by }}
                        </p>
                    {% endif %}
                    {% if tv_show.added_at %}
                        <p class="mb-1">
                            <strong><i class="bi bi-clock me-1"></i>Added:</strong>
                            {{ tv_show.added_at|date:"F d, Y" }}
                        </p>
                    {% endif %}
                {% endif %}
            </div>
        </div>

        <!-- Overview -->
        {% if tv_show.overview %}
            <div class="mb-3">
                <h5>Overview</h5>
                <p class="text-muted">{{ tv_show.overview }}</p>
            </div>
        {% endif %}

        <!-- Back Button -->
        <a href="{% url 'dashboard' %}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
        </a>
    </div>
</div>

<!-- Seasons -->
<div class="card">
    <div class="card-header">
        <h4 class="mb-0">
            <i class="bi bi-collection me-2"></i>Seasons
        </h4>
    </div>
    <div class="card-body p-0">
        {% if seasons %}
            <div class="accordion" id="seasonsAccordion">
                {% for season in seasons %}
                    <div class="accordion-item">
                        <h2 class="accordion-header position-relative" id="season{{ season.id }}Header">
                            <button class="accordion-button collapsed season-header" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#season{{ season.id }}"
                                    aria-expanded="false" aria-controls="season{{ season.id }}">
                                <div class="d-flex justify-content-between align-items-center w-100" style="margin-right: 120px;">
                                    <div>
                                        <strong>{{ season.name }}</strong>
                                        {% if season.air_date %}
                                            <small class="text-muted ms-2">
                                                <i class="bi bi-calendar me-1"></i>{{ season.air_date|date:"Y" }}
                                            </small>
                                        {% endif %}
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-secondary me-2">
                                            {{ season.available_episodes }}/{{ season.total_episodes }} episodes
                                        </span>
                                        {% if season.quality %}
                                            <span class="badge bg-info quality-badge me-2">
                                                {{ season.quality.resolution }} {{ season.quality.source }}
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </button>
                            <div class="position-absolute top-50 end-0 translate-middle-y d-flex gap-1" style="z-index: 10; margin-right: 60px;">
                                <button class="btn btn-sm btn-outline-success auto-search-btn"
                                        onclick="autoSearchSeason('{{ tv_show.title|escapejs }}', {{ season.season_number }}, {{ tv_show.id }});"
                                        title="Automatic search - select best torrent"
                                        data-season="{{ season.season_number }}">
                                    <i class="bi bi-search"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="searchSeason('{{ tv_show.title|escapejs }}', {{ season.season_number }});"
                                        title="Manual search - browse all torrents">
                                    <i class="bi bi-person"></i>
                                </button>
                            </div>
                        </h2>
                        <div id="season{{ season.id }}" class="accordion-collapse collapse"
                             aria-labelledby="season{{ season.id }}Header" data-bs-parent="#seasonsAccordion">
                            <div class="accordion-body p-0">
                                <div class="episodes-container" data-season-id="{{ season.id }}">
                                    <div class="text-center p-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading episodes...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center p-4">
                <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">No Seasons Found</h5>
                <p class="text-muted">This show doesn't have any season data yet.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Jackett Search Modal -->
<div class="modal fade" id="jackettSearchModal" tabindex="-1" aria-labelledby="jackettSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jackettSearchModalLabel">
                    <i class="bi bi-search me-2"></i><span id="searchModalTitle">Search</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="searchInfo" class="alert alert-info mb-3" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-info-circle me-2"></i>
                            <span id="searchQuery"></span>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="filterToggle" checked>
                            <label class="form-check-label" for="filterToggle">
                                <small>Filter relevant only</small>
                            </label>
                        </div>
                    </div>
                </div>

                <div id="searchLoading" class="text-center p-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Searching...</span>
                    </div>
                    <p class="mt-2 text-muted">Searching indexers...</p>
                </div>

                <div id="searchError" class="alert alert-danger" style="display: none;">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="searchErrorMessage"></span>
                </div>

                <div id="searchResults" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Search Results</h6>
                        <div class="d-flex align-items-center gap-2">
                            <button id="showAllResultsBtn" class="btn btn-sm btn-outline-secondary"
                                    style="display: none;" onclick="toggleFilter()">
                                <i class="bi bi-funnel"></i> Show All Results
                            </button>
                            <span id="resultsCount" class="badge bg-primary"></span>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Title</th>
                                    <th>Quality</th>
                                    <th>Size</th>
                                    <th>Score</th>
                                    <th>Seeders</th>
                                    <th>Leechers</th>
                                    <th>Indexer</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="noResults" class="text-center p-4" style="display: none;">
                    <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">No Results Found</h5>
                    <p class="text-muted">No torrents found for this episode. Try searching manually or check your indexers.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Episode Details Modal -->
<div class="modal fade" id="episodeDetailsModal" tabindex="-1" aria-labelledby="episodeDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="episodeDetailsModalLabel">Episode Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="episodeDetailsLoading" class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading episode details...</p>
                </div>

                <div id="episodeDetailsContent" style="display: none;">
                    <!-- Episode basic info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Episode Information</h6>
                            <p><strong>Title:</strong> <span id="episodeTitle"></span></p>
                            <p><strong>Air Date:</strong> <span id="episodeAirDate"></span></p>
                            <p><strong>Runtime:</strong> <span id="episodeRuntime"></span></p>

                            <p><strong>Quality:</strong> <span id="episodeQuality"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Overview</h6>
                            <p id="episodeOverview" class="text-muted"></p>
                        </div>
                    </div>

                    <!-- Download information -->
                    <div id="downloadInfoSection" style="display: none;">
                        <hr>
                        <h6>Download Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span id="downloadStatus"></span></p>
                                <p><strong>Torrent:</strong> <span id="downloadTorrent"></span></p>
                                <p><strong>Quality:</strong> <span id="downloadQuality"></span></p>
                                <p><strong>Size:</strong> <span id="downloadSize"></span></p>
                                <p><strong>Indexer:</strong> <span id="downloadIndexer"></span></p>
                                <p><strong>Seeders:</strong> <span id="downloadSeeders"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Started:</strong> <span id="downloadStarted"></span></p>
                                <p><strong>Completed:</strong> <span id="downloadCompleted"></span></p>
                                <p><strong>Real Debrid ID:</strong> <span id="downloadRealDebridId"></span></p>
                                <div id="downloadError" style="display: none;">
                                    <p><strong>Error:</strong> <span id="downloadErrorMessage" class="text-danger"></span></p>
                                </div>
                            </div>
                        </div>

                        <!-- Real Debrid file information -->
                        <div id="realDebridFilesSection" style="display: none;">
                            <h6 class="mt-3">Real Debrid Files</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>File</th>
                                            <th>Size</th>
                                            <th>Selected</th>
                                        </tr>
                                    </thead>
                                    <tbody id="realDebridFilesTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Download links -->
                        <div id="downloadLinksSection" style="display: none;">
                            <h6 class="mt-3">Download Links</h6>
                            <div id="downloadLinksList"></div>
                        </div>
                    </div>
                </div>

                <div id="episodeDetailsError" class="alert alert-danger" style="display: none;">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="episodeDetailsErrorMessage"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="removeEpisodeBtn" class="btn btn-danger" style="display: none;">
                    <i class="bi bi-trash"></i> Remove Download
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="toastIcon" class="bi bi-check-circle-fill text-success me-2"></i>
            <strong id="toastTitle" class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            Operation completed successfully.
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    // Global error handler for episode details
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message && event.error.message.includes('episodeDetails')) {
            console.error('Episode details error caught:', event.error);

            // Try to restore any disabled episode rows
            const disabledRows = document.querySelectorAll('tr[style*="pointer-events: none"]');
            disabledRows.forEach(row => {
                row.style.opacity = '';
                row.style.pointerEvents = '';
            });

            // Show error toast
            showToast('Episode Details Error', 'An error occurred while loading episode details. Please try again.', 'error', 5000);
        }
    });

    // Load episodes when season is expanded
    document.addEventListener('DOMContentLoaded', function() {
        const accordionButtons = document.querySelectorAll('.accordion-button');

        accordionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const target = this.getAttribute('data-bs-target');
                const seasonElement = document.querySelector(target);
                const episodesContainer = seasonElement.querySelector('.episodes-container');
                const seasonId = episodesContainer.getAttribute('data-season-id');

                // Only load if not already loaded
                if (!episodesContainer.hasAttribute('data-loaded')) {
                    loadEpisodes(seasonId, episodesContainer);
                }
            });
        });

        // Add modal event listener to reset search modal when shown
        const searchModal = document.getElementById('jackettSearchModal');
        if (searchModal) {
            searchModal.addEventListener('show.bs.modal', function() {
                console.log('Search modal opening via event listener - resetting state');
                // Small delay to ensure DOM is ready
                setTimeout(() => {
                    try {
                        resetSearchModal();
                    } catch (error) {
                        console.error('Error resetting search modal:', error);
                    }
                }, 10);
            });
        } else {
            console.warn('Search modal not found during initialization');
        }

        // Add episode details modal event listener
        const episodeDetailsModal = document.getElementById('episodeDetailsModal');
        if (episodeDetailsModal) {
            episodeDetailsModal.addEventListener('hidden.bs.modal', function() {
                console.log('Episode details modal closed - resetting state');
                resetEpisodeDetailsModal();
            });
        }
    });

    // Track if episode details are currently loading
    let isLoadingEpisodeDetails = false;

    // Episode details functionality (defined early so it's available for onclick)
    async function showEpisodeDetails(episodeId) {
        console.log('Showing details for episode:', episodeId);

        // Prevent multiple simultaneous requests
        if (isLoadingEpisodeDetails) {
            console.log('Episode details already loading, ignoring request');
            return;
        }

        isLoadingEpisodeDetails = true;

        let clickedRow = null;

        try {
            // Find the clicked row and add loading state
            if (event && event.target) {
                clickedRow = event.target.closest('tr');
                if (clickedRow) {
                    clickedRow.style.opacity = '0.6';
                    clickedRow.style.pointerEvents = 'none';
                }
            }

            // Reset modal content completely before showing
            resetEpisodeDetailsModal();

            // Check if modal exists
            const modalElement = document.getElementById('episodeDetailsModal');
            if (!modalElement) {
                console.error('Episode details modal not found');
                return;
            }

            // Show modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Update loading message with episode ID
            const loadingElement = document.getElementById('episodeDetailsLoading');
            if (loadingElement) {
                const loadingText = loadingElement.querySelector('p');
                if (loadingText) {
                    loadingText.textContent = `Loading episode ${episodeId} details...`;
                }
            }

            try {
                // Add timeout to prevent hanging
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

                const response = await fetch(`/api/episode/${episodeId}/details/`, {
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success) {
                    populateEpisodeDetails(data.episode);
                    document.getElementById('episodeDetailsLoading').style.display = 'none';
                    document.getElementById('episodeDetailsContent').style.display = 'block';
                } else {
                    showEpisodeDetailsError(data.error || 'Failed to load episode details');
                }
            } catch (error) {
                console.error('Error loading episode details:', error);

                let errorMessage = 'Failed to load episode details';
                if (error.name === 'AbortError') {
                    errorMessage = 'Request timed out. Please try again.';
                } else if (error.message) {
                    errorMessage += ': ' + error.message;
                }

                showEpisodeDetailsError(errorMessage);
            }
        } catch (outerError) {
            console.error('Outer error in showEpisodeDetails:', outerError);
            showEpisodeDetailsError('An unexpected error occurred');
        } finally {
            // Reset loading flag
            isLoadingEpisodeDetails = false;

            // Restore row state
            if (clickedRow) {
                clickedRow.style.opacity = '';
                clickedRow.style.pointerEvents = '';
            }
        }
    }

    async function loadEpisodes(seasonId, container) {
        console.log('Loading episodes for season:', seasonId);
        try {
            const response = await fetch(`/season/${seasonId}/episodes/`);
            console.log('Episodes response status:', response.status);
            const data = await response.json();
            console.log('Episodes data:', data);

            if (data.episodes && data.episodes.length > 0) {
                let episodesHtml = '<div class="table-responsive"><table class="table table-sm mb-0">';
                episodesHtml += '<thead class="table-light">';
                episodesHtml += '<tr><th>Episode</th><th>Title</th><th>Air Date</th><th>Runtime</th><th>Quality</th><th>Actions</th></tr>';
                episodesHtml += '</thead><tbody>';

                data.episodes.forEach(episode => {
                    const qualityBadge = episode.quality ?
                        `<span class="badge bg-info quality-badge">${episode.quality.resolution} ${episode.quality.source}</span>` :
                        '<span class="text-muted">-</span>';

                    // Generate action buttons based on download status
                    let actionButtons = '';

                    if (episode.download_status) {
                        const downloadStatus = episode.download_status.status;

                        switch (downloadStatus) {
                            case 'pending':
                                actionButtons = `
                                    <button class="btn btn-sm btn-warning" disabled title="Download pending - monitoring for completion">
                                        <i class="bi bi-clock"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                            title="Manual search - browse all torrents">
                                        <i class="bi bi-person"></i>
                                    </button>`;
                                break;
                            case 'downloading':
                                actionButtons = `
                                    <button class="btn btn-sm btn-info" disabled title="Downloading from Real Debrid">
                                        <i class="bi bi-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                            title="Manual search - browse all torrents">
                                        <i class="bi bi-person"></i>
                                    </button>`;
                                break;
                            case 'monitoring':
                                actionButtons = `
                                    <button class="btn btn-sm btn-secondary" disabled title="Download complete - monitoring for Plex availability">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                            title="Manual search - browse all torrents">
                                        <i class="bi bi-person"></i>
                                    </button>`;
                                break;
                            case 'completed':
                                actionButtons = `
                                    <button class="btn btn-sm btn-success" disabled title="Download completed and available in Plex">
                                        <i class="bi bi-check-circle"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                            title="Manual search - browse all torrents">
                                        <i class="bi bi-person"></i>
                                    </button>`;
                                break;
                            case 'failed':
                                const errorTitle = episode.download_status.error_message ?
                                    `Download failed: ${episode.download_status.error_message}` :
                                    'Download failed - click to retry';
                                actionButtons = `
                                    <button class="btn btn-sm btn-danger"
                                            onclick="autoSearchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, {{ tv_show.id }})"
                                            title="${errorTitle}"
                                            data-season="${data.season.season_number}" data-episode="${episode.episode_number}">
                                        <i class="bi bi-exclamation-triangle"></i> Failed
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                            title="Manual search - browse all torrents">
                                        <i class="bi bi-person"></i>
                                    </button>`;
                                break;
                            default:
                                // Default buttons for unknown status
                                actionButtons = `
                                    <button class="btn btn-sm btn-outline-success auto-search-btn"
                                            onclick="autoSearchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, {{ tv_show.id }})"
                                            title="Automatic search - select best torrent"
                                            data-season="${data.season.season_number}" data-episode="${episode.episode_number}">
                                        <i class="bi bi-search"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                            title="Manual search - browse all torrents">
                                        <i class="bi bi-person"></i>
                                    </button>`;
                        }
                    } else {
                        // No download status - show default buttons
                        actionButtons = `
                            <button class="btn btn-sm btn-outline-success auto-search-btn"
                                    onclick="autoSearchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, {{ tv_show.id }})"
                                    title="Automatic search - select best torrent"
                                    data-season="${data.season.season_number}" data-episode="${episode.episode_number}">
                                <i class="bi bi-search"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, ${episode.id})"
                                    title="Manual search - browse all torrents">
                                <i class="bi bi-person"></i>
                            </button>`;
                    }

                    episodesHtml += `<tr class="episode-row" onclick="showEpisodeDetails(${episode.id})" style="cursor: pointer;">`;
                    episodesHtml += `<td><strong>E${episode.episode_number.toString().padStart(2, '0')}</strong></td>`;
                    episodesHtml += `<td>${episode.name || 'Episode ' + episode.episode_number}</td>`;
                    episodesHtml += `<td>${episode.air_date ? new Date(episode.air_date).toLocaleDateString() : '-'}</td>`;
                    episodesHtml += `<td>${episode.runtime ? episode.runtime + ' min' : '-'}</td>`;
                    episodesHtml += `<td>${qualityBadge}</td>`;
                    episodesHtml += `<td onclick="event.stopPropagation();">
                        <div class="d-flex gap-1">
                            ${actionButtons}
                        </div>
                    </td>`;
                    episodesHtml += '</tr>';
                });

                episodesHtml += '</tbody></table></div>';
                container.innerHTML = episodesHtml;
            } else {
                container.innerHTML = '<div class="text-center p-3"><p class="text-muted mb-0">No episodes found for this season.</p></div>';
            }

            container.setAttribute('data-loaded', 'true');

        } catch (error) {
            console.error('Error loading episodes:', error);
            container.innerHTML = '<div class="text-center p-3"><p class="text-danger mb-0">Failed to load episodes. Please try again.</p></div>';
        }
    }

    // Global variables for current search
    let currentSearchParams = null;

    // Jackett search functionality
    function searchEpisode(showTitle, season, episode, episodeId) {
        console.log('Starting new search for:', showTitle, 'S' + season + 'E' + episode);

        // Store search parameters
        currentSearchParams = {
            type: 'episode',
            showTitle: showTitle,
            season: season,
            episode: episode,
            episodeId: episodeId
        };
        // Update modal title with specific episode information
        const episodeText = `S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}`;
        document.getElementById('searchModalTitle').textContent = `Search for ${episodeText}`;

        // Show modal and reset state
        const modal = new bootstrap.Modal(document.getElementById('jackettSearchModal'));
        modal.show();

        // Reset modal state after showing
        try {
            resetSearchModal();
        } catch (error) {
            console.error('Error resetting search modal:', error);
        }

        // Update search info
        const searchQuery = `${showTitle} S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}`;
        document.getElementById('searchQuery').textContent = `Searching for: ${searchQuery}`;
        document.getElementById('searchInfo').style.display = 'block';

        // Show loading state
        document.getElementById('searchLoading').style.display = 'block';

        // Perform search
        performJackettSearch(showTitle, season, episode, 'episode');
    }

    function searchSeason(showTitle, season) {
        // Store search parameters
        currentSearchParams = {
            type: 'season',
            showTitle: showTitle,
            season: season
        };

        // Update modal title with specific season information
        document.getElementById('searchModalTitle').textContent = `Search for Season ${season}`;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('jackettSearchModal'));
        modal.show();

        // Update search info
        const searchQuery = `${showTitle} Season ${season}`;
        document.getElementById('searchQuery').textContent = `Searching for: ${searchQuery}`;
        document.getElementById('searchInfo').style.display = 'block';

        // Show loading state
        resetSearchModal();
        document.getElementById('searchLoading').style.display = 'block';

        // Perform search
        performJackettSeasonSearch(showTitle, season);
    }

    function resetSearchModal() {
        console.log('Resetting search modal...');

        try {
            // Most important: clear the results table
            const tbody = document.getElementById('resultsTableBody');
            if (tbody) {
                tbody.innerHTML = '';
                console.log('Cleared results table');
            }

            // Hide all sections
            const sections = ['searchError', 'searchResults', 'noResults'];
            sections.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = 'none';
                }
            });

            // Show loading
            const searchLoading = document.getElementById('searchLoading');
            if (searchLoading) {
                searchLoading.style.display = 'block';
            }

            console.log('Search modal reset complete');
        } catch (error) {
            console.error('Error in resetSearchModal:', error);
        }
    }

    async function performJackettSearch(showTitle, season, episode, searchType = 'episode') {
        try {
            const filterEnabled = document.getElementById('filterToggle').checked;
            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                episode: episode,
                indexer: 'all',
                filter: filterEnabled ? 'true' : 'false',
                _t: Date.now() // Cache buster
            });

            const response = await fetch(`/api/jackett/search/?${params}`, {
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });
            const data = await response.json();

            // Hide loading
            document.getElementById('searchLoading').style.display = 'none';

            if (data.success && data.results && data.results.length > 0) {
                displaySearchResults(data.results, searchType, data.filtered);
            } else if (data.error) {
                showSearchError(data.error);
            } else {
                document.getElementById('noResults').style.display = 'block';
            }

        } catch (error) {
            console.error('Search error:', error);
            document.getElementById('searchLoading').style.display = 'none';
            showSearchError('Failed to search. Please check your Jackett configuration.');
        }
    }

    async function performJackettSeasonSearch(showTitle, season) {
        try {
            const filterEnabled = document.getElementById('filterToggle').checked;
            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                indexer: 'all',
                filter: filterEnabled ? 'true' : 'false',
                _t: Date.now() // Cache buster
            });

            const response = await fetch(`/api/jackett/search-season/?${params}`, {
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });
            const data = await response.json();

            // Hide loading
            document.getElementById('searchLoading').style.display = 'none';

            if (data.success && data.results && data.results.length > 0) {
                displaySearchResults(data.results, 'season', data.filtered);
            } else if (data.error) {
                showSearchError(data.error);
            } else {
                document.getElementById('noResults').style.display = 'block';
            }

        } catch (error) {
            console.error('Season search error:', error);
            document.getElementById('searchLoading').style.display = 'none';
            showSearchError('Failed to search season. Please check your Jackett configuration.');
        }
    }

    function displaySearchResults(results, searchType = 'episode', isFiltered = true) {
        console.log('Displaying search results:', results.length, 'results of type:', searchType);

        const tbody = document.getElementById('resultsTableBody');
        tbody.innerHTML = '';

        // Update results count
        const searchTypeText = searchType === 'season' ? 'season packs' : 'episodes';
        document.getElementById('resultsCount').textContent = `${results.length} ${searchTypeText}`;

        // Show/hide the "Show All Results" button
        const showAllBtn = document.getElementById('showAllResultsBtn');
        if (isFiltered && results.length > 0) {
            showAllBtn.style.display = 'block';
            showAllBtn.innerHTML = '<i class="bi bi-funnel"></i> Show All Results';
        } else {
            showAllBtn.style.display = 'none';
        }

        results.forEach(result => {
            const row = document.createElement('tr');

            // Truncate title if too long
            const title = result.title.length > 60 ? result.title.substring(0, 60) + '...' : result.title;

            // Quality badge color based on quality
            let qualityBadgeClass = 'bg-info';
            if (result.quality === '4K') qualityBadgeClass = 'bg-danger';
            else if (result.quality === '1080p') qualityBadgeClass = 'bg-success';
            else if (result.quality === '720p') qualityBadgeClass = 'bg-warning';
            else if (result.quality === '480p') qualityBadgeClass = 'bg-secondary';

            // Score badge color based on score value
            let scoreBadgeClass = 'bg-secondary';
            let scoreText = result.score || 0;
            if (scoreText > 1000) scoreBadgeClass = 'bg-success';
            else if (scoreText > 0) scoreBadgeClass = 'bg-info';
            else if (scoreText < 0) scoreBadgeClass = 'bg-danger';

            row.innerHTML = `
                <td>
                    <div class="fw-bold">${title}</div>
                    <small class="text-muted">${result.description || ''}</small>
                </td>
                <td><span class="badge ${qualityBadgeClass}">${result.quality}</span></td>
                <td><strong>${result.size_formatted}</strong></td>
                <td>
                    <span class="badge ${scoreBadgeClass}" title="Quality profile score">
                        ${scoreText}
                    </span>
                </td>
                <td>
                    <span class="badge bg-success">
                        <i class="bi bi-arrow-up"></i> ${result.seeders}
                    </span>
                </td>
                <td>
                    <span class="badge bg-warning">
                        <i class="bi bi-arrow-down"></i> ${result.leechers}
                    </span>
                </td>
                <td><small class="text-muted">${result.indexer}</small></td>
                <td>
                    <button class="btn btn-sm btn-primary"
                            onclick="downloadTorrent('${result.download_url}', '${result.magnet_url}', '${result.title.replace(/'/g, "\\'")}', '${result.quality}', '${result.indexer}', ${result.size}, ${result.seeders})">
                        <i class="bi bi-download"></i> Download
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });

        document.getElementById('searchResults').style.display = 'block';
    }

    function showSearchError(message) {
        document.getElementById('searchErrorMessage').textContent = message;
        document.getElementById('searchError').style.display = 'block';
    }

    async function downloadTorrent(downloadUrl, magnetUrl, title, quality, indexer, size, seeders) {
        try {
            // Check if we have episode information from the current search
            if (!currentSearchParams || !currentSearchParams.episodeId) {
                alert('Episode information not available. Please try the search again.');
                return;
            }

            // Ensure we have at least one URL
            if (!downloadUrl && !magnetUrl) {
                showToast('Download Error', 'No download URL or magnet URL available', 'error');
                return;
            }

            const response = await fetch('/api/jackett/download/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    download_url: downloadUrl || '',
                    magnet_url: magnetUrl || '',
                    title: title,
                    episode_id: currentSearchParams.episodeId,
                    quality: quality,
                    indexer: indexer,
                    size: size,
                    seeders: seeders
                })
            });

            const data = await response.json();

            if (data.success) {
                if (data.cached) {
                    showToast('Download Started', data.message, 'success', 8000);

                    // Close the search modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('jackettSearchModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // Refresh the page to show updated status
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showToast('Not Cached', data.message, 'warning', 8000);
                }
            } else {
                showToast('Download Failed', data.error || 'Unknown error occurred', 'error', 8000);

                // Refresh the episode list to show failed status
                setTimeout(() => {
                    const seasonContainer = document.querySelector('.accordion-collapse.show');
                    if (seasonContainer) {
                        const episodesContainer = seasonContainer.querySelector('.episodes-container');
                        const seasonId = episodesContainer.getAttribute('data-season-id');

                        // Reload episodes to show updated download status
                        episodesContainer.removeAttribute('data-loaded');
                        loadEpisodes(seasonId, episodesContainer);
                    }
                }, 1000);
            }

        } catch (error) {
            console.error('Download error:', error);
            showToast('Download Error', 'Failed to start download: ' + error.message, 'error', 8000);

            // Refresh the episode list to show failed status
            setTimeout(() => {
                const seasonContainer = document.querySelector('.accordion-collapse.show');
                if (seasonContainer) {
                    const episodesContainer = seasonContainer.querySelector('.episodes-container');
                    const seasonId = episodesContainer.getAttribute('data-season-id');

                    // Reload episodes to show updated download status
                    episodesContainer.removeAttribute('data-loaded');
                    loadEpisodes(seasonId, episodesContainer);
                }
            }, 1000);
        }
    }

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Reset episode details modal to clean state
    function resetEpisodeDetailsModal() {
        console.log('Resetting episode details modal...');

        try {
            // Helper function to safely update element
            function safeUpdateElement(id, property, value) {
                const element = document.getElementById(id);
                if (element) {
                    if (property === 'textContent') {
                        element.textContent = value;
                    } else if (property === 'innerHTML') {
                        element.innerHTML = value;
                    } else if (property === 'style.display') {
                        element.style.display = value;
                    }
                } else {
                    console.warn(`Element with id '${id}' not found during modal reset`);
                }
            }

            // Reset modal title
            safeUpdateElement('episodeDetailsModalLabel', 'textContent', 'Episode Details');

            // Show loading, hide content and error
            safeUpdateElement('episodeDetailsLoading', 'style.display', 'block');
            safeUpdateElement('episodeDetailsContent', 'style.display', 'none');
            safeUpdateElement('episodeDetailsError', 'style.display', 'none');
            safeUpdateElement('removeEpisodeBtn', 'style.display', 'none');

            // Clear all content fields
            safeUpdateElement('episodeTitle', 'textContent', '');
            safeUpdateElement('episodeAirDate', 'textContent', '');
            safeUpdateElement('episodeRuntime', 'textContent', '');

            safeUpdateElement('episodeQuality', 'textContent', '');
            safeUpdateElement('episodeOverview', 'textContent', '');

            // Hide download sections
            safeUpdateElement('downloadInfoSection', 'style.display', 'none');
            safeUpdateElement('realDebridFilesSection', 'style.display', 'none');
            safeUpdateElement('downloadLinksSection', 'style.display', 'none');
            safeUpdateElement('downloadError', 'style.display', 'none');

            // Clear download info fields
            safeUpdateElement('downloadStatus', 'innerHTML', '');
            safeUpdateElement('downloadTorrent', 'textContent', '');
            safeUpdateElement('downloadQuality', 'textContent', '');
            safeUpdateElement('downloadSize', 'textContent', '');
            safeUpdateElement('downloadIndexer', 'textContent', '');
            safeUpdateElement('downloadSeeders', 'textContent', '');
            safeUpdateElement('downloadStarted', 'textContent', '');
            safeUpdateElement('downloadCompleted', 'textContent', '');
            safeUpdateElement('downloadRealDebridId', 'textContent', '');
            safeUpdateElement('downloadErrorMessage', 'textContent', '');

            // Clear tables
            safeUpdateElement('realDebridFilesTable', 'innerHTML', '');
            safeUpdateElement('downloadLinksList', 'innerHTML', '');

            console.log('Episode details modal reset complete');

        } catch (error) {
            console.error('Error resetting episode details modal:', error);
        }
    }

    // Episode details functionality is defined earlier in the script

    function populateEpisodeDetails(episode) {
        try {
            console.log('Populating episode details for:', episode.id);

            // Helper function to safely update element
            function safeSetElement(id, property, value) {
                const element = document.getElementById(id);
                if (element) {
                    if (property === 'textContent') {
                        element.textContent = value;
                    } else if (property === 'innerHTML') {
                        element.innerHTML = value;
                    } else if (property === 'style.display') {
                        element.style.display = value;
                    } else if (property === 'onclick') {
                        element.onclick = value;
                    }
                    return true;
                } else {
                    console.warn(`Element with id '${id}' not found during populate`);
                    return false;
                }
            }

            // Basic episode info
            const episodeTitle = `${episode.season.tv_show.title} S${episode.season.season_number.toString().padStart(2, '0')}E${episode.episode_number.toString().padStart(2, '0')}`;
            safeSetElement('episodeDetailsModalLabel', 'textContent', episodeTitle);

            safeSetElement('episodeTitle', 'textContent', episode.name || `Episode ${episode.episode_number}`);
            safeSetElement('episodeAirDate', 'textContent', episode.air_date ? new Date(episode.air_date).toLocaleDateString() : 'Unknown');
            safeSetElement('episodeRuntime', 'textContent', episode.runtime ? `${episode.runtime} minutes` : 'Unknown');



            // Quality
            if (episode.quality) {
                const qualityBadge = `<span class="badge bg-info">${episode.quality.resolution} ${episode.quality.source} ${episode.quality.codec}</span>`;
                safeSetElement('episodeQuality', 'innerHTML', qualityBadge);
            } else {
                safeSetElement('episodeQuality', 'textContent', 'Unknown');
            }

            // Overview
            safeSetElement('episodeOverview', 'textContent', episode.overview || 'No overview available.');

            // Download information
            if (episode.download_info) {
                populateDownloadInfo(episode.download_info, episode.id);
                safeSetElement('downloadInfoSection', 'style.display', 'block');
                safeSetElement('removeEpisodeBtn', 'style.display', 'inline-block');
                safeSetElement('removeEpisodeBtn', 'onclick', () => removeEpisodeDownload(episode.id));
            } else {
                safeSetElement('downloadInfoSection', 'style.display', 'none');
            }

            console.log('Episode details populated successfully');

        } catch (error) {
            console.error('Error populating episode details:', error);
            showEpisodeDetailsError('Failed to display episode details: ' + error.message);
        }
    }

    function populateDownloadInfo(downloadInfo, episodeId) {
        // Download status
        const statusElement = document.getElementById('downloadStatus');
        let statusClass = 'secondary';
        switch (downloadInfo.status) {
            case 'completed': statusClass = 'success'; break;
            case 'failed': statusClass = 'danger'; break;
            case 'downloading': statusClass = 'info'; break;
            case 'monitoring': statusClass = 'warning'; break;
            case 'pending': statusClass = 'warning'; break;
        }
        statusElement.innerHTML = `<span class="badge bg-${statusClass}">${downloadInfo.status}</span>`;

        // Download details
        document.getElementById('downloadTorrent').textContent = downloadInfo.torrent_title || 'Unknown';
        document.getElementById('downloadQuality').textContent = downloadInfo.quality || 'Unknown';
        document.getElementById('downloadSize').textContent = downloadInfo.size_formatted || 'Unknown';
        document.getElementById('downloadIndexer').textContent = downloadInfo.indexer || 'Unknown';
        document.getElementById('downloadSeeders').textContent = downloadInfo.seeders || 'Unknown';
        document.getElementById('downloadStarted').textContent = downloadInfo.started_at ?
            new Date(downloadInfo.started_at).toLocaleString() : 'Unknown';
        document.getElementById('downloadCompleted').textContent = downloadInfo.completed_at ?
            new Date(downloadInfo.completed_at).toLocaleString() : 'Not completed';
        document.getElementById('downloadRealDebridId').textContent = downloadInfo.real_debrid_id || 'None';

        // Error message
        if (downloadInfo.error_message) {
            document.getElementById('downloadError').style.display = 'block';
            document.getElementById('downloadErrorMessage').textContent = downloadInfo.error_message;
        } else {
            document.getElementById('downloadError').style.display = 'none';
        }

        // Real Debrid file information
        if (downloadInfo.real_debrid_info && downloadInfo.real_debrid_info.files) {
            populateRealDebridFiles(downloadInfo.real_debrid_info.files);
            document.getElementById('realDebridFilesSection').style.display = 'block';
        } else {
            document.getElementById('realDebridFilesSection').style.display = 'none';
        }

        // Download links
        if (downloadInfo.real_debrid_info && downloadInfo.real_debrid_info.links) {
            populateDownloadLinks(downloadInfo.real_debrid_info.links);
            document.getElementById('downloadLinksSection').style.display = 'block';
        } else {
            document.getElementById('downloadLinksSection').style.display = 'none';
        }
    }

    function populateRealDebridFiles(files) {
        const tbody = document.getElementById('realDebridFilesTable');
        tbody.innerHTML = '';

        files.forEach(file => {
            const row = tbody.insertRow();

            // File name
            const nameCell = row.insertCell();
            nameCell.textContent = file.path || 'Unknown';

            // File size
            const sizeCell = row.insertCell();
            sizeCell.textContent = formatFileSize(file.bytes || 0);

            // Selected status
            const selectedCell = row.insertCell();
            selectedCell.innerHTML = file.selected ?
                '<span class="badge bg-success">Yes</span>' :
                '<span class="badge bg-secondary">No</span>';
        });
    }

    function populateDownloadLinks(links) {
        const container = document.getElementById('downloadLinksList');
        container.innerHTML = '';

        links.forEach((link, index) => {
            const linkElement = document.createElement('div');
            linkElement.className = 'mb-2';
            linkElement.innerHTML = `
                <a href="${link}" target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-download"></i> Download Link ${index + 1}
                </a>
            `;
            container.appendChild(linkElement);
        });
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    function showEpisodeDetailsError(message) {
        document.getElementById('episodeDetailsLoading').style.display = 'none';
        document.getElementById('episodeDetailsContent').style.display = 'none';
        document.getElementById('episodeDetailsError').style.display = 'block';
        document.getElementById('episodeDetailsErrorMessage').textContent = message;
    }

    async function removeEpisodeDownload(episodeId) {
        if (!confirm('Are you sure you want to remove this download? This will also remove it from Real Debrid.')) {
            return;
        }

        try {
            const response = await fetch(`/api/episode/${episodeId}/remove/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();

            if (data.success) {
                showToast('Download Removed', data.message, 'success', 5000);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('episodeDetailsModal'));
                if (modal) {
                    modal.hide();
                }

                // Refresh the episode list
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast('Removal Failed', data.error || 'Failed to remove download', 'error', 5000);
            }
        } catch (error) {
            console.error('Error removing episode download:', error);
            showToast('Removal Error', 'Failed to remove download: ' + error.message, 'error', 5000);
        }
    }

    // Toggle filter function
    function toggleFilter() {
        if (!currentSearchParams) return;

        // Toggle the filter checkbox
        const filterToggle = document.getElementById('filterToggle');
        filterToggle.checked = !filterToggle.checked;

        // Re-run the search with new filter setting
        resetSearchModal();
        document.getElementById('searchLoading').style.display = 'block';

        if (currentSearchParams.type === 'episode') {
            performJackettSearch(
                currentSearchParams.showTitle,
                currentSearchParams.season,
                currentSearchParams.episode,
                'episode'
            );
        } else if (currentSearchParams.type === 'season') {
            performJackettSeasonSearch(
                currentSearchParams.showTitle,
                currentSearchParams.season
            );
        }
    }

    // Toast notification functions
    function showToast(title, message, type = 'success', duration = 5000) {
        const toast = document.getElementById('notificationToast');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        const toastIcon = document.getElementById('toastIcon');

        // Set content
        toastTitle.textContent = title;
        toastMessage.innerHTML = message; // Use innerHTML to support HTML content

        // Set icon and colors based on type
        if (type === 'success') {
            toastIcon.className = 'bi bi-check-circle-fill text-success me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-success-subtle';
        } else if (type === 'error') {
            toastIcon.className = 'bi bi-exclamation-triangle-fill text-danger me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-danger-subtle';
        } else if (type === 'warning') {
            toastIcon.className = 'bi bi-exclamation-triangle-fill text-warning me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-warning-subtle';
        } else if (type === 'info') {
            toastIcon.className = 'bi bi-info-circle-fill text-info me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-info-subtle';
        }

        // Show the toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: duration
        });
        bsToast.show();

        return bsToast; // Return the toast instance for manual control
    }

    // Automatic search functions
    async function autoSearchEpisode(showTitle, season, episode, showId) {
        // Find the button that was clicked
        const button = document.querySelector(`button.auto-search-btn[data-season="${season}"][data-episode="${episode}"]`);

        try {
            // Disable button and show spinner
            if (button) {
                button.disabled = true;
                const icon = button.querySelector('i');
                // Replace the icon with a proper spinner
                icon.className = 'spinner-border spinner-border-sm';
                icon.setAttribute('role', 'status');
                icon.setAttribute('aria-hidden', 'true');
            }

            // Show loading indicator
            const loadingToast = showToast('Searching...', 'Automatically searching for best torrent...', 'info', 0); // No auto-hide
            const searchStartTime = Date.now();

            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                episode: episode,
                show_id: showId
            });

            const response = await fetch(`/api/jackett/auto-download/?${params}`);
            const data = await response.json();

            // Ensure loading toast is visible for at least 1.5 seconds
            const searchDuration = Date.now() - searchStartTime;
            const minDisplayTime = 1500;
            const remainingTime = Math.max(0, minDisplayTime - searchDuration);

            setTimeout(() => {
                // Hide loading toast
                if (loadingToast && loadingToast.hide) {
                    loadingToast.hide();
                }

                // Show result toast after a brief delay
                setTimeout(() => {
                    if (data.success) {
                        const torrent = data.torrent;
                        const message = `
                            <strong>Download started:</strong><br>
                            <strong>Title:</strong> ${torrent.title}<br>
                            <strong>Quality:</strong> ${torrent.quality}<br>
                            <strong>Size:</strong> ${torrent.size}<br>
                            <strong>Seeders:</strong> ${torrent.seeders}<br>
                            <strong>Indexer:</strong> ${torrent.indexer}<br>
                            <strong>Profile used:</strong> ${data.profile_used}<br>
                            <strong>Results:</strong> ${data.filtered_results}/${data.total_results} matching torrents<br>
                            <strong>Tried:</strong> ${data.tried_count} torrents
                        `;

                        showToast('Download Started', message, 'success', 10000);

                        // Refresh the episode list to show updated status
                        const seasonContainer = button.closest('.accordion-collapse');
                        if (seasonContainer) {
                            const episodesContainer = seasonContainer.querySelector('.episodes-container');
                            const seasonId = episodesContainer.getAttribute('data-season-id');

                            // Reload episodes to show updated download status
                            episodesContainer.removeAttribute('data-loaded');
                            loadEpisodes(seasonId, episodesContainer);
                        }
                    } else {
                        const errorMessage = data.error || 'Unknown error occurred';
                        let detailMessage = errorMessage;

                        if (data.tried_count) {
                            detailMessage += `<br><strong>Tried:</strong> ${data.tried_count} torrents`;
                        }
                        if (data.filtered_results && data.total_results) {
                            detailMessage += `<br><strong>Results:</strong> ${data.filtered_results}/${data.total_results} matching torrents`;
                        }

                        showToast('Download Failed', detailMessage, 'error', 8000);
                    }
                }, 200);
            }, remainingTime);
        } catch (error) {
            console.error('Auto search failed:', error);
            showToast('Auto Search Error', 'Failed to perform automatic search: ' + error.message, 'error');
        } finally {
            // Re-enable button and restore icon
            if (button) {
                button.disabled = false;
                const icon = button.querySelector('i');
                icon.className = 'bi bi-search';
                icon.removeAttribute('role');
                icon.removeAttribute('aria-hidden');
            }
        }
    }

    async function autoSearchSeason(showTitle, season, showId) {
        // Find the button that was clicked
        const button = document.querySelector(`button.auto-search-btn[data-season="${season}"]:not([data-episode])`);

        try {
            // Disable button and show spinner
            if (button) {
                button.disabled = true;
                const icon = button.querySelector('i');
                // Replace the icon with a proper spinner
                icon.className = 'spinner-border spinner-border-sm';
                icon.setAttribute('role', 'status');
                icon.setAttribute('aria-hidden', 'true');
            }

            // Show loading indicator
            const loadingToast = showToast('Searching...', 'Automatically searching for best season pack...', 'info', 0); // No auto-hide
            const searchStartTime = Date.now();

            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                show_id: showId
            });

            const response = await fetch(`/api/jackett/auto-search-season/?${params}`);
            const data = await response.json();

            // Ensure loading toast is visible for at least 1.5 seconds
            const searchDuration = Date.now() - searchStartTime;
            const minDisplayTime = 1500;
            const remainingTime = Math.max(0, minDisplayTime - searchDuration);

            setTimeout(() => {
                // Hide loading toast
                if (loadingToast && loadingToast.hide) {
                    loadingToast.hide();
                }

                // Show result toast after a brief delay
                setTimeout(() => {
                    if (data.success) {
                        const torrent = data.torrent;
                        const message = `
                            <strong>Best season pack selected:</strong><br>
                            <strong>Title:</strong> ${torrent.title}<br>
                            <strong>Quality:</strong> ${torrent.quality}<br>
                            <strong>Size:</strong> ${torrent.size}<br>
                            <strong>Seeders:</strong> ${torrent.seeders}<br>
                            <strong>Indexer:</strong> ${torrent.indexer}<br>
                            <strong>Profile used:</strong> ${data.profile_used}<br>
                            <strong>Results:</strong> ${data.filtered_results}/${data.total_results} matching torrents
                        `;

                        showToast('Auto Search Complete', message, 'success', 10000);
                    } else {
                        showToast('Auto Search Failed', data.error || 'Unknown error occurred', 'error');
                    }
                }, 200);
            }, remainingTime);
        } catch (error) {
            console.error('Auto season search failed:', error);
            showToast('Auto Search Error', 'Failed to perform automatic season search: ' + error.message, 'error');
        } finally {
            // Re-enable button and restore icon
            if (button) {
                button.disabled = false;
                const icon = button.querySelector('i');
                icon.className = 'bi bi-search';
                icon.removeAttribute('role');
                icon.removeAttribute('aria-hidden');
            }
        }
    }

    // Profile management functions
    async function loadAvailableProfiles() {
        try {
            const response = await fetch('/api/profiles/');
            const data = await response.json();

            if (data.success) {
                const container = document.getElementById('profileDropdownItems');
                container.innerHTML = '';

                data.profiles.forEach(profile => {
                    const li = document.createElement('li');
                    const isActive = {{ tv_show.quality_profile.id|default:'null' }} === profile.id;

                    li.innerHTML = `
                        <a class="dropdown-item ${isActive ? 'active' : ''}"
                           href="#" onclick="assignProfile(${profile.id})">
                            <i class="bi bi-sliders me-1"></i>${profile.name}
                            ${profile.is_default ? '<span class="badge bg-primary ms-1">Default</span>' : ''}
                        </a>
                    `;

                    container.appendChild(li);
                });
            }
        } catch (error) {
            console.error('Failed to load profiles:', error);
        }
    }

    async function assignProfile(profileId) {
        try {
            const response = await fetch(`/shows/{{ tv_show.id }}/assign-profile/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    profile_id: profileId
                })
            });

            const data = await response.json();

            if (data.success) {
                // Reload the page to update the profile display
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        } catch (error) {
            console.error('Failed to assign profile:', error);
            alert('Failed to assign profile: ' + error.message);
        }
    }

    // Load available profiles on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadAvailableProfiles();
    });
</script>
{% endblock %}
