{% extends 'base.html' %}

{% block title %}Custom Formats{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'profiles_list' %}">Quality Profiles</a></li>
                            <li class="breadcrumb-item active">Custom Formats</li>
                        </ol>
                    </nav>
                    <h1><i class="bi bi-tags me-2"></i>Custom Formats</h1>
                </div>
                <button class="btn btn-primary" onclick="createNewFormat()">
                    <i class="bi bi-plus-circle me-1"></i>New Custom Format
                </button>
            </div>

            <div class="row">
                {% for format in custom_formats %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">{{ format.name }}</h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editFormat({{ format.id }})">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="duplicateFormat({{ format.id }})">
                                        <i class="bi bi-files me-1"></i>Duplicate
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteFormat({{ format.id }}, '{{ format.name|escapejs }}')">
                                        <i class="bi bi-trash me-1"></i>Delete
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if format.description %}
                                <p class="card-text text-muted">{{ format.description }}</p>
                            {% endif %}
                            
                            <div class="mb-3">
                                <h6 class="text-muted">Include Patterns:</h6>
                                {% if format.include_patterns %}
                                    {% for pattern in format.include_patterns %}
                                        <span class="badge bg-success me-1 mb-1">{{ pattern }}</span>
                                    {% endfor %}
                                {% else %}
                                    <small class="text-muted">No include patterns</small>
                                {% endif %}
                            </div>
                            
                            {% if format.exclude_patterns %}
                            <div class="mb-3">
                                <h6 class="text-muted">Exclude Patterns:</h6>
                                {% for pattern in format.exclude_patterns %}
                                    <span class="badge bg-danger me-1 mb-1">{{ pattern }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <div class="text-muted small">
                                <i class="bi bi-sliders me-1"></i>
                                Used in {{ format.customformatprofilescore_set.count }} profile{{ format.customformatprofilescore_set.count|pluralize }}
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="badge {% if format.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                {% if format.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-tags text-muted" style="font-size: 3rem;"></i>
                        <h3 class="mt-3 text-muted">No Custom Formats</h3>
                        <p class="text-muted">Create your first custom format to get started.</p>
                        <button class="btn btn-primary" onclick="createNewFormat()">
                            <i class="bi bi-plus-circle me-1"></i>Create Custom Format
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Custom Format Modal -->
<div class="modal fade" id="customFormatModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customFormatModalTitle">Create Custom Format</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customFormatForm">
                    <input type="hidden" id="formatId" value="">
                    <div class="mb-3">
                        <label for="formatName" class="form-label">Format Name</label>
                        <input type="text" class="form-control" id="formatName" required>
                    </div>
                    <div class="mb-3">
                        <label for="formatDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="formatDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="includePatterns" class="form-label">Include Patterns</label>
                        <textarea class="form-control" id="includePatterns" rows="4" 
                                  placeholder="Enter regex patterns (one per line)&#10;Example:&#10;(?i)x265&#10;(?i)h\.?265&#10;(?i)hevc"></textarea>
                        <div class="form-text">Patterns that must be present in the release name (regex supported)</div>
                    </div>
                    <div class="mb-3">
                        <label for="excludePatterns" class="form-label">Exclude Patterns</label>
                        <textarea class="form-control" id="excludePatterns" rows="4" 
                                  placeholder="Enter regex patterns (one per line)&#10;Example:&#10;(?i)2160p&#10;(?i)4k&#10;(?i)uhd"></textarea>
                        <div class="form-text">Patterns that must NOT be present in the release name (regex supported)</div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="formatActive" checked>
                        <label class="form-check-label" for="formatActive">
                            Active
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitCustomFormat()">Save Format</button>
            </div>
        </div>
    </div>
</div>

<script>
let editingFormatId = null;

function createNewFormat() {
    editingFormatId = null;
    document.getElementById('customFormatModalTitle').textContent = 'Create Custom Format';
    document.getElementById('customFormatForm').reset();
    document.getElementById('formatId').value = '';
    document.getElementById('formatActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('customFormatModal'));
    modal.show();
}

function editFormat(formatId) {
    editingFormatId = formatId;
    document.getElementById('customFormatModalTitle').textContent = 'Edit Custom Format';
    
    // Load format data
    fetch(`/profiles/custom-formats/${formatId}/api/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const format = data.format;
                document.getElementById('formatId').value = format.id;
                document.getElementById('formatName').value = format.name;
                document.getElementById('formatDescription').value = format.description;
                document.getElementById('includePatterns').value = format.include_patterns.join('\n');
                document.getElementById('excludePatterns').value = format.exclude_patterns.join('\n');
                document.getElementById('formatActive').checked = format.is_active;
                
                const modal = new bootstrap.Modal(document.getElementById('customFormatModal'));
                modal.show();
            } else {
                alert('Error loading format: ' + data.error);
            }
        })
        .catch(error => {
            alert('Failed to load format: ' + error.message);
        });
}

async function submitCustomFormat() {
    const name = document.getElementById('formatName').value;
    const description = document.getElementById('formatDescription').value;
    const includePatterns = document.getElementById('includePatterns').value
        .split('\n')
        .map(p => p.trim())
        .filter(p => p.length > 0);
    const excludePatterns = document.getElementById('excludePatterns').value
        .split('\n')
        .map(p => p.trim())
        .filter(p => p.length > 0);
    const isActive = document.getElementById('formatActive').checked;
    
    if (!name.trim()) {
        alert('Please enter a format name');
        return;
    }
    
    const url = editingFormatId ? 
        `/profiles/custom-formats/${editingFormatId}/update/` : 
        '/profiles/custom-formats/create/';
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                name: name,
                description: description,
                include_patterns: includePatterns,
                exclude_patterns: excludePatterns,
                is_active: isActive
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    } catch (error) {
        alert('Failed to save format: ' + error.message);
    }
}

async function deleteFormat(formatId, formatName) {
    if (!confirm(`Are you sure you want to delete the custom format "${formatName}"?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/profiles/custom-formats/${formatId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    } catch (error) {
        alert('Failed to delete format: ' + error.message);
    }
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
