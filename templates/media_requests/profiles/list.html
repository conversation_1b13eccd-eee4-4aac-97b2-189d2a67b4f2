{% extends 'base.html' %}

{% block title %}Quality Profiles{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-sliders me-2"></i>Quality Profiles</h1>
                <div>
                    <a href="{% url 'custom_formats_list' %}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-tags me-1"></i>Custom Formats
                    </a>
                    <button class="btn btn-primary" onclick="createNewProfile()">
                        <i class="bi bi-plus-circle me-1"></i>New Profile
                    </button>
                </div>
            </div>

            <div class="row">
                {% for profile in profiles %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 {% if profile.is_default %}border-primary{% endif %}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                {{ profile.name }}
                                {% if profile.is_default %}
                                    <span class="badge bg-primary ms-2">Default</span>
                                {% endif %}
                            </h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'profile_detail' profile.id %}">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="duplicateProfile({{ profile.id }})">
                                        <i class="bi bi-files me-1"></i>Duplicate
                                    </a></li>
                                    {% if not profile.is_default %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteProfile({{ profile.id }}, '{{ profile.name|escapejs }}')">
                                        <i class="bi bi-trash me-1"></i>Delete
                                    </a></li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if profile.description %}
                                <p class="card-text text-muted">{{ profile.description }}</p>
                            {% endif %}

                            <div class="mb-3">
                                <h6 class="text-muted">Allowed Qualities:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    {% for item in profile.quality_items.all %}
                                        {% if item.allowed %}
                                            <span class="badge bg-info">{{ item.quality.resolution }}</span>
                                        {% endif %}
                                    {% empty %}
                                        <small class="text-muted">No qualities configured</small>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-muted">Settings:</h6>
                                <ul class="list-unstyled small">
                                    <li>
                                        <i class="bi bi-arrow-up-circle me-1"></i>
                                        Upgrades:
                                        {% if profile.allow_upgrades %}
                                            <span class="text-success">Enabled</span>
                                        {% else %}
                                            <span class="text-danger">Disabled</span>
                                        {% endif %}
                                    </li>
                                    {% if profile.upgrade_until_quality %}
                                    <li>
                                        <i class="bi bi-stop-circle me-1"></i>
                                        Upgrade Until: <span class="badge bg-secondary">{{ profile.upgrade_until_quality.name }}</span>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>

                            <div class="text-muted small">
                                <i class="bi bi-tv me-1"></i>
                                Used by {{ profile.tvshow_set.count }} show{{ profile.tvshow_set.count|pluralize }}
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="{% url 'profile_detail' profile.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-gear me-1"></i>Configure
                            </a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-sliders text-muted" style="font-size: 3rem;"></i>
                        <h3 class="mt-3 text-muted">No Quality Profiles</h3>
                        <p class="text-muted">Create your first quality profile to get started.</p>
                        <button class="btn btn-primary" onclick="createNewProfile()">
                            <i class="bi bi-plus-circle me-1"></i>Create Profile
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Create Profile Modal -->
<div class="modal fade" id="createProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createProfileForm">
                    <div class="mb-3">
                        <label for="profileName" class="form-label">Profile Name</label>
                        <input type="text" class="form-control" id="profileName" required>
                    </div>
                    <div class="mb-3">
                        <label for="profileDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="profileDescription" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="allowUpgrades" checked>
                        <label class="form-check-label" for="allowUpgrades">
                            Allow Upgrades
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateProfile()">Create Profile</button>
            </div>
        </div>
    </div>
</div>

<script>
function createNewProfile() {
    const modal = new bootstrap.Modal(document.getElementById('createProfileModal'));
    modal.show();
}

async function submitCreateProfile() {
    const name = document.getElementById('profileName').value;
    const description = document.getElementById('profileDescription').value;
    const allowUpgrades = document.getElementById('allowUpgrades').checked;

    if (!name.trim()) {
        alert('Please enter a profile name');
        return;
    }

    try {
        const response = await fetch('/profiles/create/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                name: name,
                description: description,
                allow_upgrades: allowUpgrades
            })
        });

        const data = await response.json();

        if (data.success) {
            window.location.href = `/profiles/${data.profile_id}/`;
        } else {
            alert('Error: ' + data.error);
        }
    } catch (error) {
        alert('Failed to create profile: ' + error.message);
    }
}

async function deleteProfile(profileId, profileName) {
    if (!confirm(`Are you sure you want to delete the profile "${profileName}"?`)) {
        return;
    }

    try {
        const response = await fetch(`/profiles/${profileId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const data = await response.json();

        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    } catch (error) {
        alert('Failed to delete profile: ' + error.message);
    }
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
