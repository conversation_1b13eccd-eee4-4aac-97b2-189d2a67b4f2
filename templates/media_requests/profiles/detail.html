{% extends 'base.html' %}

{% block title %}{{ profile.name }} - Quality Profile{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'profiles_list' %}">Quality Profiles</a></li>
                            <li class="breadcrumb-item active">{{ profile.name }}</li>
                        </ol>
                    </nav>
                    <h1>
                        <i class="bi bi-sliders me-2"></i>{{ profile.name }}
                        {% if profile.is_default %}
                            <span class="badge bg-primary ms-2">Default</span>
                        {% endif %}
                    </h1>
                </div>
                <div>
                    <button class="btn btn-success" onclick="saveProfile()">
                        <i class="bi bi-check-circle me-1"></i>Save Changes
                    </button>
                    <a href="{% url 'profiles_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>Back to Profiles
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Basic Settings -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gear me-2"></i>Basic Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="profileName" class="form-label">Profile Name</label>
                                <input type="text" class="form-control" id="profileName" value="{{ profile.name }}">
                            </div>
                            <div class="mb-3">
                                <label for="profileDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="profileDescription" rows="3">{{ profile.description }}</textarea>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="allowUpgrades"
                                       {% if profile.allow_upgrades %}checked{% endif %}>
                                <label class="form-check-label" for="allowUpgrades">
                                    Allow Upgrades
                                </label>
                                <div class="form-text">Allow upgrading existing files to better quality</div>
                            </div>
                            <div class="mb-3">
                                <label for="upgradeUntilQuality" class="form-label">Upgrade Until Quality</label>
                                <select class="form-select" id="upgradeUntilQuality">
                                    <option value="">No limit</option>
                                    {% for quality in all_qualities %}
                                        <option value="{{ quality.id }}"
                                                {% if profile.upgrade_until_quality and profile.upgrade_until_quality.id == quality.id %}selected{% endif %}>
                                            {{ quality.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Stop upgrading once this quality is reached</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quality Settings -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-collection-play me-2"></i>Allowed Qualities
                            </h5>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="selectAllQualities()">
                                    Select All
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="deselectAllQualities()">
                                    Deselect All
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="form-text mb-3">
                                Select which quality levels are allowed for download. Higher qualities will be preferred.
                            </div>
                            <div id="qualityList">
                                {% for quality in all_qualities %}
                                <div class="form-check mb-2 quality-item" data-quality-id="{{ quality.id }}" data-priority="{{ quality.priority }}">
                                    <input class="form-check-input quality-checkbox" type="checkbox"
                                           id="quality_{{ quality.id }}" value="{{ quality.id }}"
                                           {% if quality.id in profile_quality_ids %}checked{% endif %}>
                                    <label class="form-check-label d-flex justify-content-between align-items-center w-100"
                                           for="quality_{{ quality.id }}">
                                        <div>
                                            <strong>{{ quality.resolution }}</strong>
                                            {% if quality.source %}
                                                <span class="text-muted">{{ quality.source }}</span>
                                            {% endif %}
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-secondary me-2">{{ quality.priority }}</span>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm move-up"
                                                        onclick="moveQualityUp({{ quality.id }})" title="Move Up">
                                                    <i class="bi bi-arrow-up"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm move-down"
                                                        onclick="moveQualityDown({{ quality.id }})" title="Move Down">
                                                    <i class="bi bi-arrow-down"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Formats -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-tags me-2"></i>Custom Format Scores
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="form-text mb-3">
                                Assign scores to custom formats. Positive scores increase preference, negative scores decrease it.
                                Range: -10000 to +10000
                            </div>
                            <div class="row">
                                {% for format in all_custom_formats %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-light">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">{{ format.name }}</h6>
                                                <input type="number" class="form-control form-control-sm custom-format-score"
                                                       style="width: 80px;" min="-10000" max="10000"
                                                       data-format-id="{{ format.id }}"
                                                       value="{{ format_score_dict|default:0 }}"
                                                       placeholder="0">
                                            </div>
                                            {% if format.description %}
                                                <p class="card-text small text-muted">{{ format.description }}</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Usage -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-tv me-2"></i>Shows Using This Profile
                            </h5>
                        </div>
                        <div class="card-body">
                            {% with shows_using_profile=profile.tvshow_set.all %}
                                {% if shows_using_profile %}
                                    <div class="row">
                                        {% for show in shows_using_profile %}
                                        <div class="col-md-6 col-lg-4 mb-2">
                                            <a href="{% url 'show_detail' show.id %}" class="text-decoration-none">
                                                <div class="d-flex align-items-center p-2 border rounded hover-bg-light">
                                                    {% if show.poster_path %}
                                                        <img src="https://image.tmdb.org/t/p/w200{{ show.poster_path }}" alt="{{ show.title }}"
                                                             class="rounded me-2" style="width: 40px; height: 60px; object-fit: cover;">
                                                    {% else %}
                                                        <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center"
                                                             style="width: 40px; height: 60px;">
                                                            <i class="bi bi-tv text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ show.title }}</div>
                                                        <small class="text-muted">{{ show.first_air_date|date:"Y" }}</small>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-center py-3">
                                        <i class="bi bi-tv text-muted" style="font-size: 2rem;"></i>
                                        <p class="text-muted mt-2">No shows are currently using this profile.</p>
                                    </div>
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-bg-light:hover {
    background-color: #f8f9fa !important;
}

.quality-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem !important;
}

.quality-item .form-check-input {
    margin-right: 0.75rem;
}

.quality-item:hover {
    background-color: #f8f9fa;
}

.custom-format-score:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>

<script>
async function saveProfile() {
    try {
        // Collect form data
        const profileData = {
            name: document.getElementById('profileName').value,
            description: document.getElementById('profileDescription').value,
            allow_upgrades: document.getElementById('allowUpgrades').checked,
            upgrade_until_quality: document.getElementById('upgradeUntilQuality').value || null,
            quality_items: [],
            custom_format_scores: []
        };

        // Collect quality items
        const qualityCheckboxes = document.querySelectorAll('.quality-checkbox');
        qualityCheckboxes.forEach((checkbox, index) => {
            if (checkbox.checked) {
                const qualityItem = checkbox.closest('.quality-item');
                profileData.quality_items.push({
                    quality_id: parseInt(checkbox.value),
                    allowed: true,
                    order: parseInt(qualityItem.dataset.priority)
                });
            }
        });

        // Collect custom format scores
        const scoreInputs = document.querySelectorAll('.custom-format-score');
        scoreInputs.forEach(input => {
            const score = parseInt(input.value) || 0;
            if (score !== 0) {
                profileData.custom_format_scores.push({
                    format_id: parseInt(input.dataset.formatId),
                    score: score
                });
            }
        });

        // Send update request
        const response = await fetch(`/profiles/{{ profile.id }}/update/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(profileData)
        });

        const data = await response.json();

        if (data.success) {
            // Show success message
            showAlert('success', data.message);
        } else {
            showAlert('danger', 'Error: ' + data.error);
        }

    } catch (error) {
        showAlert('danger', 'Failed to save profile: ' + error.message);
    }
}

function selectAllQualities() {
    const checkboxes = document.querySelectorAll('.quality-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function deselectAllQualities() {
    const checkboxes = document.querySelectorAll('.quality-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}

function moveQualityUp(qualityId) {
    const qualityItem = document.querySelector(`[data-quality-id="${qualityId}"]`);
    const prevItem = qualityItem.previousElementSibling;
    if (prevItem) {
        qualityItem.parentNode.insertBefore(qualityItem, prevItem);
        updateQualityOrder();
    }
}

function moveQualityDown(qualityId) {
    const qualityItem = document.querySelector(`[data-quality-id="${qualityId}"]`);
    const nextItem = qualityItem.nextElementSibling;
    if (nextItem) {
        qualityItem.parentNode.insertBefore(nextItem, qualityItem);
        updateQualityOrder();
    }
}

function updateQualityOrder() {
    const qualityItems = document.querySelectorAll('.quality-item');
    qualityItems.forEach((item, index) => {
        const priority = qualityItems.length - index;
        item.dataset.priority = priority;
        const badge = item.querySelector('.badge');
        if (badge) {
            badge.textContent = priority;
        }
    });
}

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-dismissible');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize custom format scores
document.addEventListener('DOMContentLoaded', function() {
    const scoreInputs = document.querySelectorAll('.custom-format-score');
    scoreInputs.forEach(input => {
        const formatId = input.dataset.formatId;
        const currentScore = {{ format_score_dict|safe }}[formatId] || 0;
        input.value = currentScore;
    });
});
</script>
{% endblock %}
