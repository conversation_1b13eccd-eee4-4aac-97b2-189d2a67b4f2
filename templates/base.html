<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Overseerr Dashboard{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --bs-primary: #6366f1;
            --bs-secondary: #64748b;
            --bs-success: #10b981;
            --bs-warning: #f59e0b;
            --bs-danger: #ef4444;
        }

        .navbar-brand {
            font-weight: 600;
        }

        .card-hover {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .poster-img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 0.375rem;
        }

        .availability-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        .season-header {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .season-header:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .episode-row {
            border-left: 3px solid transparent;
        }

        .episode-row.available {
            border-left-color: var(--bs-success);
        }

        .episode-row.unavailable {
            border-left-color: var(--bs-danger);
        }

        .quality-badge {
            font-size: 0.75rem;
        }

        .stats-card {
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .loading-text {
            display: none;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="bi bi-tv me-2"></i>
                Overseerr Dashboard
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}"
                           href="{% url 'dashboard' %}">
                            <i class="bi bi-house me-1"></i>Dashboard
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm" onclick="syncData()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            <span class="loading-text">Sync Data</span>
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                        </button>
                    </li>
                    <li class="nav-item ms-2">
                        <button class="btn btn-outline-light btn-sm" onclick="checkJackettStatus()" title="Check Jackett Status">
                            <i class="bi bi-search me-1"></i>Jackett
                        </button>
                    </li>
                    <li class="nav-item ms-2">
                        <a class="btn btn-outline-light btn-sm" href="/profiles/">
                            <i class="bi bi-sliders me-1"></i>Profiles
                        </a>
                    </li>
                    <li class="nav-item ms-2">
                        <a class="btn btn-outline-light btn-sm" href="/admin/" target="_blank">
                            <i class="bi bi-gear me-1"></i>Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center text-muted">
            <p class="mb-0">
                <i class="bi bi-tv me-1"></i>
                Overseerr Dashboard - Manage your media requests
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Sync data function
        async function syncData() {
            const button = document.querySelector('[onclick="syncData()"]');
            button.classList.add('loading');

            try {
                const response = await fetch('{% url "sync_data" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    location.reload();
                } else {
                    throw new Error('Sync failed');
                }
            } catch (error) {
                console.error('Sync error:', error);
                alert('Failed to sync data. Please try again.');
            } finally {
                button.classList.remove('loading');
            }
        }

        // Get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Check Jackett status
        async function checkJackettStatus() {
            try {
                const response = await fetch('/api/jackett/status/');
                const data = await response.json();

                if (data.status === 'connected') {
                    let message = `Jackett Status: Connected ✓\n\n`;
                    message += `Base URL: ${data.config.base_url}\n`;
                    message += `API Key: ${data.config.api_key_configured ? 'Configured ✓' : 'Not configured ✗'}\n`;
                    message += `Indexers: ${data.total_indexers} total, ${data.configured_indexers} configured`;
                    alert(message);
                } else {
                    let message = `Jackett Status: Connection Failed ✗\n\n`;
                    message += `Error: ${data.message || 'Unknown error'}\n`;
                    if (data.config) {
                        message += `Base URL: ${data.config.base_url}\n`;
                        message += `API Key: ${data.config.api_key_configured ? 'Configured' : 'Not configured'}\n`;
                    }
                    if (data.help) {
                        message += `\nHelp: ${data.help}`;
                    }
                    alert(message);
                }
            } catch (error) {
                console.error('Jackett status check failed:', error);
                alert('Failed to check Jackett status. Please check your configuration.\n\nError: ' + error.message);
            }
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
