# Overseerr Dashboard

A Django-based web application that integrates with the Overseerr API to display media requests in a Sonarr-like interface. The application focuses on TV shows and displays their availability status and quality information.

## Features

- **TV Show Management**: Display TV shows in a hierarchical view (Show → Seasons → Episodes)
- **Availability Tracking**: Show availability status for each season and episode
- **Quality Information**: Display quality information for available content
- **Jackett Search Integration**: Search for specific episodes across multiple indexers using Jackett
- **Episode Search**: Manual search for individual episodes with quality, size, and seeder information
- **Collapsible UI**: Expandable shows and seasons for better organization
- **Filtering & Sorting**: Filter by availability status and sort by various criteria
- **Search Functionality**: Search shows by title or description
- **Responsive Design**: Bootstrap-based responsive UI
- **API Integration**: Seamless integration with Overseerr and Jackett APIs

## Tech Stack

- **Backend**: Django 4.2.23 with SQLite database
- **Frontend**: Django template engine with Bootstrap 5.3
- **API Integration**: Overseerr API client, Jackett API integration
- **Styling**: Bootstrap 5.3 with custom CSS
- **Icons**: Bootstrap Icons

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd davver
   ```

2. **Create and activate virtual environment**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your Overseerr and Jackett configuration:
   ```
   # Overseerr Configuration
   OVERSEERR_BASE_URL=http://your-overseerr-instance:5055
   OVERSEERR_API_KEY=your_api_key_here

   # Plex Configuration (optional)
   PLEX_BASE_URL=http://your-plex-instance:32400
   PLEX_TOKEN=your_plex_token

   # Jackett Configuration
   JACKETT_BASE_URL=http://your-jackett-instance:9117
   JACKETT_API_KEY=your_jackett_api_key

   # Django Configuration
   SECRET_KEY=your_django_secret_key
   DEBUG=True
   ALLOWED_HOSTS=localhost,127.0.0.1
   ```

5. **Run database migrations**:
   ```bash
   python manage.py migrate
   ```

6. **Create sample data** (optional):
   ```bash
   python manage.py create_sample_data
   ```

7. **Start the development server**:
   ```bash
   python manage.py runserver
   ```

8. **Access the application**:
   Open your browser and navigate to `http://127.0.0.1:8000/`

## Usage

### Syncing Data from Overseerr

1. **Manual Sync**: Click the "Sync Data" button in the navigation bar
2. **Command Line**: Run `python manage.py sync_overseerr`
3. **Automated Sync**: Set up a cron job or scheduled task to run the sync command regularly

### Dashboard Features

- **Statistics Cards**: View total shows and availability breakdown
- **Search**: Use the search bar to find specific shows
- **Filters**: Filter shows by availability status (Available, Partial, Unavailable)
- **Sorting**: Sort shows by title, date added, air date, or request date
- **Show Cards**: Click "View Details" to see detailed information about a show

### Show Details

- **Show Information**: View poster, overview, air dates, and status
- **Season Management**: Expand seasons to view episode details
- **Episode Information**: See episode availability, quality, and air dates
- **Quality Indicators**: Color-coded badges showing resolution and source

### Jackett Search Integration

The application includes a powerful search feature that integrates with Jackett to find torrents for episodes and complete seasons:

- **Episode Search**: Click the search button (🔍) next to any episode to search for individual episode torrents
- **Season Search**: Click the search button (🔍) next to any season header to search for complete season packs
- **Multiple Indexers**: Searches across all configured Jackett indexers simultaneously
- **Quality-Based Sorting**: Results are sorted by quality first (4K → 1080p → 720p → 480p), then by seeders
- **Detailed Results**: View title, quality, file size, seeders, leechers, and indexer for each result
- **Quality Detection**: Automatically detects quality (4K, 1080p, 720p, etc.) from torrent titles
- **Color-Coded Quality**: Quality badges are color-coded (4K=red, 1080p=green, 720p=yellow, 480p=gray)
- **Download Placeholder**: Download buttons show alerts (actual download integration to be implemented)

#### Jackett Configuration

1. **Install Jackett**: Download and install Jackett from [GitHub](https://github.com/Jackett/Jackett)
2. **Configure Indexers**: Set up your preferred torrent indexers in Jackett
3. **Get API Key**: Copy your API key from the Jackett dashboard
4. **Update .env**: Add your Jackett URL and API key to the `.env` file
5. **Test Connection**: Use the "Jackett" button in the navigation to test connectivity

#### Search Features

- **Smart Query Building**: Automatically formats search queries for episodes (e.g., "Show Name S01E01") and seasons (e.g., "Show Name Season 1")
- **Multiple Query Formats**: Tries different search patterns to maximize results
- **Quality-First Sorting**: Results prioritized by quality (4K > 1080p > 720p > 480p), then by seeders
- **Result Deduplication**: Removes duplicate results based on GUID
- **Season Pack Detection**: Season searches look for complete season downloads and season packs
- **Visual Quality Indicators**: Color-coded quality badges for easy identification
- **Error Handling**: Graceful handling of indexer failures and network issues

## Data Models

- **TVShow**: Contains basic show info and list of seasons
- **Season**: Contains season number, availability status, quality profile, and episodes list
- **Episode**: Contains episode number, name, availability status, quality, and air date
- **MediaRequest**: Contains request metadata from Overseerr
- **MediaQuality**: Contains resolution, source, and codec information

## API Endpoints

### Main Application
- `/` - Main dashboard
- `/show/<id>/` - Show detail page
- `/season/<id>/episodes/` - AJAX endpoint for episode data
- `/sync/` - Manual data synchronization

### API Endpoints
- `/api/status/` - Overseerr API connection status check
- `/api/search/` - Search TV shows via Overseerr
- `/api/add/` - Add TV show to database
- `/api/delete/<id>/` - Delete TV show from database

### Jackett Integration
- `/api/jackett/search/` - Search for episodes using Jackett
- `/api/jackett/search-season/` - Search for complete seasons using Jackett
- `/api/jackett/status/` - Check Jackett connection and indexer status
- `/api/jackett/download/` - Handle torrent download requests (placeholder)

## Management Commands

- `python manage.py sync_overseerr` - Sync data from Overseerr API
- `python manage.py create_sample_data` - Create sample data for testing

## Testing

Run the test suite:
```bash
python manage.py test
```

The test suite includes:
- Model tests for data relationships and methods
- View tests for HTTP responses and functionality
- Service tests for API integration
- Integration tests for complete workflows

## Configuration

### Overseerr API Setup

1. In Overseerr, go to Settings → General → API
2. Generate an API key
3. Add the API key to your `.env` file as `OVERSEERR_API_KEY`
4. Set the base URL in `.env` as `OVERSEERR_BASE_URL`

### Django Admin

Create a superuser to access the Django admin:
```bash
python manage.py createsuperuser
```

Access the admin at `http://127.0.0.1:8000/admin/`

## Development

### Project Structure

```
davver/
├── overseerr_dashboard/          # Django project settings
├── media_requests/               # Main application
│   ├── models.py                # Data models
│   ├── views.py                 # View controllers
│   ├── services.py              # API integration services
│   ├── admin.py                 # Django admin configuration
│   ├── tests.py                 # Test suite
│   └── management/commands/     # Management commands
├── templates/                   # HTML templates
├── static/                      # Static files (CSS, JS)
├── requirements.txt             # Python dependencies
└── .env                        # Environment configuration
```

### Adding New Features

1. Update models in `media_requests/models.py`
2. Create and run migrations: `python manage.py makemigrations && python manage.py migrate`
3. Update views in `media_requests/views.py`
4. Update templates in `templates/media_requests/`
5. Add tests in `media_requests/tests.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions, please create an issue in the repository or contact the development team.
