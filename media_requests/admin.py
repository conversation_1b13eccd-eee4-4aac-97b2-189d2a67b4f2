from django.contrib import admin
from .models import (
    MediaQuality, MediaRequest, TVShow, Season, Episode,
    QualityDefinition, QualityProfile, QualityProfileItem,
    CustomFormat, CustomFormatProfileScore
)


@admin.register(MediaQuality)
class MediaQualityAdmin(admin.ModelAdmin):
    list_display = ['resolution', 'source', 'codec']
    list_filter = ['resolution', 'source', 'codec']
    search_fields = ['resolution', 'source', 'codec']


@admin.register(MediaRequest)
class MediaRequestAdmin(admin.ModelAdmin):
    list_display = ['overseerr_id', 'media_type', 'status', 'requested_by', 'requested_at']
    list_filter = ['media_type', 'status', 'requested_at']
    search_fields = ['overseerr_id', 'requested_by']
    readonly_fields = ['overseerr_id', 'requested_at', 'updated_at']


class SeasonInline(admin.TabularInline):
    model = Season
    extra = 0
    readonly_fields = ['created_at', 'updated_at']


@admin.register(TVShow)
class TVShowAdmin(admin.ModelAdmin):
    list_display = ['title', 'quality_profile', 'first_air_date', 'status']
    list_filter = ['quality_profile', 'status', 'first_air_date']
    search_fields = ['title', 'tmdb_id']
    readonly_fields = ['tmdb_id', 'created_at', 'updated_at']
    inlines = [SeasonInline]


class EpisodeInline(admin.TabularInline):
    model = Episode
    extra = 0
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Season)
class SeasonAdmin(admin.ModelAdmin):
    list_display = ['tv_show', 'season_number', 'air_date']
    list_filter = ['air_date']
    search_fields = ['tv_show__title', 'name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [EpisodeInline]


@admin.register(Episode)
class EpisodeAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'quality', 'air_date', 'runtime']
    list_filter = ['quality', 'air_date']
    search_fields = ['name', 'season__tv_show__title']
    readonly_fields = ['created_at', 'updated_at']


# Quality Profile Admin

@admin.register(QualityDefinition)
class QualityDefinitionAdmin(admin.ModelAdmin):
    list_display = ['name', 'resolution', 'source', 'priority', 'is_active']
    list_filter = ['resolution', 'source', 'is_active']
    search_fields = ['name', 'resolution', 'source']
    ordering = ['-priority', 'name']


class QualityProfileItemInline(admin.TabularInline):
    model = QualityProfileItem
    extra = 0
    ordering = ['-order', 'quality__priority']


class CustomFormatProfileScoreInline(admin.TabularInline):
    model = CustomFormatProfileScore
    extra = 0
    ordering = ['-score', 'custom_format__name']


@admin.register(QualityProfile)
class QualityProfileAdmin(admin.ModelAdmin):
    list_display = ['name', 'allow_upgrades', 'upgrade_until_quality', 'is_default', 'is_active']
    list_filter = ['allow_upgrades', 'is_default', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [QualityProfileItemInline, CustomFormatProfileScoreInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'is_default', 'is_active')
        }),
        ('Upgrade Settings', {
            'fields': ('allow_upgrades', 'upgrade_until_quality')
        }),
        ('Custom Format Settings', {
            'fields': ('minimum_custom_format_score', 'upgrade_until_custom_format_score')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(CustomFormat)
class CustomFormatAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Pattern Matching', {
            'fields': ('include_patterns', 'exclude_patterns'),
            'description': 'Use regex patterns to match release names'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
