from django.core.management.base import BaseCommand
from media_requests.models import QualityDefinition, QualityProfile, QualityProfileItem, CustomFormat, CustomFormatProfileScore


class Command(BaseCommand):
    help = 'Set up default quality definitions, profiles, and custom formats'

    def handle(self, *args, **options):
        self.stdout.write('Setting up quality profiles...')
        
        # Create Quality Definitions
        self.create_quality_definitions()
        
        # Create Custom Formats
        self.create_custom_formats()
        
        # Create Default Profiles
        self.create_default_profiles()
        
        self.stdout.write(self.style.SUCCESS('Quality profiles setup completed!'))

    def create_quality_definitions(self):
        """Create standard quality definitions"""
        qualities = [
            # SD Qualities
            {'name': 'SDTV', 'resolution': '480p', 'source': 'HDTV', 'priority': 10},
            {'name': 'DVD', 'resolution': '480p', 'source': 'DVD', 'priority': 20},
            {'name': 'WEBDL-480p', 'resolution': '480p', 'source': 'WEB-DL', 'priority': 30},
            
            # HD Qualities
            {'name': 'HDTV-720p', 'resolution': '720p', 'source': 'HDTV', 'priority': 40},
            {'name': 'WEBDL-720p', 'resolution': '720p', 'source': 'WEB-DL', 'priority': 50},
            {'name': 'Bluray-720p', 'resolution': '720p', 'source': 'BluRay', 'priority': 60},
            
            {'name': 'HDTV-1080p', 'resolution': '1080p', 'source': 'HDTV', 'priority': 70},
            {'name': 'WEBDL-1080p', 'resolution': '1080p', 'source': 'WEB-DL', 'priority': 80},
            {'name': 'Bluray-1080p', 'resolution': '1080p', 'source': 'BluRay', 'priority': 90},
            {'name': 'Remux-1080p', 'resolution': '1080p', 'source': 'Remux', 'priority': 100},
            
            # 4K Qualities
            {'name': 'WEBDL-2160p', 'resolution': '4K', 'source': 'WEB-DL', 'priority': 110},
            {'name': 'Bluray-2160p', 'resolution': '4K', 'source': 'BluRay', 'priority': 120},
            {'name': 'Remux-2160p', 'resolution': '4K', 'source': 'Remux', 'priority': 130},
        ]
        
        for quality_data in qualities:
            quality, created = QualityDefinition.objects.get_or_create(
                name=quality_data['name'],
                defaults=quality_data
            )
            if created:
                self.stdout.write(f'Created quality: {quality.name}')

    def create_custom_formats(self):
        """Create common custom formats"""
        formats = [
            # Unwanted formats
            {
                'name': 'BR-DISK',
                'description': 'Blocks BR-DISK releases',
                'include_patterns': [r'(?i)br[\s\.]?disk', r'(?i)bd[\s\.]?disk', r'(?i)blu[\s\.]?ray[\s\.]?disk'],
                'exclude_patterns': []
            },
            {
                'name': 'LQ Groups',
                'description': 'Low quality release groups',
                'include_patterns': [r'(?i)-YIFY', r'(?i)-YTS', r'(?i)-RARBG', r'(?i)-FGT'],
                'exclude_patterns': []
            },
            {
                'name': 'x265 (HD)',
                'description': 'Blocks x265 HD releases',
                'include_patterns': [r'(?i)x265', r'(?i)h\.?265', r'(?i)hevc'],
                'exclude_patterns': [r'(?i)2160p', r'(?i)4k', r'(?i)uhd']
            },
            
            # Preferred formats
            {
                'name': 'WEB Tier 01',
                'description': 'Top tier WEB release groups',
                'include_patterns': [r'(?i)-NTb', r'(?i)-AMZN', r'(?i)-ATVP', r'(?i)-HMAX'],
                'exclude_patterns': []
            },
            {
                'name': 'WEB Tier 02',
                'description': 'Second tier WEB release groups',
                'include_patterns': [r'(?i)-NF', r'(?i)-DSNP', r'(?i)-HULU'],
                'exclude_patterns': []
            },
            
            # Quality indicators
            {
                'name': 'Repack/Proper',
                'description': 'Repack and Proper releases',
                'include_patterns': [r'(?i)repack', r'(?i)proper'],
                'exclude_patterns': []
            },
            {
                'name': 'HDR',
                'description': 'HDR content',
                'include_patterns': [r'(?i)hdr', r'(?i)hdr10', r'(?i)dolby[\s\.]?vision', r'(?i)dv'],
                'exclude_patterns': []
            },
        ]
        
        for format_data in formats:
            custom_format, created = CustomFormat.objects.get_or_create(
                name=format_data['name'],
                defaults=format_data
            )
            if created:
                self.stdout.write(f'Created custom format: {custom_format.name}')

    def create_default_profiles(self):
        """Create default quality profiles"""
        
        # HD Profile (720p/1080p)
        hd_profile, created = QualityProfile.objects.get_or_create(
            name='HD (720p/1080p)',
            defaults={
                'description': 'Standard HD profile for 720p and 1080p content',
                'allow_upgrades': True,
                'is_default': True,
            }
        )
        
        if created:
            self.stdout.write(f'Created profile: {hd_profile.name}')
            
            # Set upgrade until quality
            upgrade_quality = QualityDefinition.objects.filter(name='Remux-1080p').first()
            if upgrade_quality:
                hd_profile.upgrade_until_quality = upgrade_quality
                hd_profile.save()
            
            # Add allowed qualities
            hd_qualities = QualityDefinition.objects.filter(
                resolution__in=['720p', '1080p']
            ).order_by('priority')
            
            for i, quality in enumerate(hd_qualities):
                QualityProfileItem.objects.create(
                    profile=hd_profile,
                    quality=quality,
                    allowed=True,
                    order=quality.priority
                )
            
            # Add custom format scores
            self.add_custom_format_scores(hd_profile, 'hd')
        
        # 4K Profile
        uhd_profile, created = QualityProfile.objects.get_or_create(
            name='4K (UHD)',
            defaults={
                'description': '4K UHD profile for 2160p content',
                'allow_upgrades': True,
            }
        )
        
        if created:
            self.stdout.write(f'Created profile: {uhd_profile.name}')
            
            # Set upgrade until quality
            upgrade_quality = QualityDefinition.objects.filter(name='Remux-2160p').first()
            if upgrade_quality:
                uhd_profile.upgrade_until_quality = upgrade_quality
                uhd_profile.save()
            
            # Add allowed qualities (include 1080p for fallback)
            uhd_qualities = QualityDefinition.objects.filter(
                resolution__in=['1080p', '4K']
            ).order_by('priority')
            
            for quality in uhd_qualities:
                QualityProfileItem.objects.create(
                    profile=uhd_profile,
                    quality=quality,
                    allowed=True,
                    order=quality.priority
                )
            
            # Add custom format scores
            self.add_custom_format_scores(uhd_profile, '4k')
        
        # Any Quality Profile
        any_profile, created = QualityProfile.objects.get_or_create(
            name='Any Quality',
            defaults={
                'description': 'Accept any quality, upgrade to best available',
                'allow_upgrades': True,
            }
        )
        
        if created:
            self.stdout.write(f'Created profile: {any_profile.name}')
            
            # Add all qualities
            all_qualities = QualityDefinition.objects.all().order_by('priority')
            
            for quality in all_qualities:
                QualityProfileItem.objects.create(
                    profile=any_profile,
                    quality=quality,
                    allowed=True,
                    order=quality.priority
                )
            
            # Add custom format scores
            self.add_custom_format_scores(any_profile, 'any')

    def add_custom_format_scores(self, profile, profile_type):
        """Add custom format scores to a profile"""
        
        # Common unwanted formats (negative scores)
        unwanted_scores = {
            'BR-DISK': -10000,
            'LQ Groups': -10000,
            'x265 (HD)': -10000 if profile_type != '4k' else 0,
        }
        
        # Preferred formats (positive scores)
        preferred_scores = {
            'WEB Tier 01': 1700,
            'WEB Tier 02': 1650,
            'Repack/Proper': 5,
            'HDR': 500 if profile_type == '4k' else 0,
        }
        
        all_scores = {**unwanted_scores, **preferred_scores}
        
        for format_name, score in all_scores.items():
            custom_format = CustomFormat.objects.filter(name=format_name).first()
            if custom_format:
                CustomFormatProfileScore.objects.get_or_create(
                    profile=profile,
                    custom_format=custom_format,
                    defaults={'score': score}
                )
