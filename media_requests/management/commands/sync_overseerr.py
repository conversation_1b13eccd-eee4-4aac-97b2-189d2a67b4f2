from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from media_requests.services import OverseerrDataSyncService, OverseerrAPIError


class Command(BaseCommand):
    help = 'Sync Plex availability and quality data for existing TV shows'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making any database changes',
        )

    def handle(self, *args, **options):
        # Check if API key is configured
        if not settings.OVERSEERR_API_KEY:
            raise CommandError(
                'OVERSEERR_API_KEY is not configured. Please set it in your .env file.'
            )

        if not settings.OVERSEERR_BASE_URL:
            raise CommandError(
                'OVERSEERR_BASE_URL is not configured. Please set it in your .env file.'
            )

        self.stdout.write(
            self.style.SUCCESS('Starting Plex data synchronization...')
        )

        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No database changes will be made')
            )
            return

        try:
            sync_service = OverseerrDataSyncService()
            sync_service.sync_all_tv_requests()

            self.stdout.write(
                self.style.SUCCESS('Successfully completed Plex data synchronization')
            )

        except OverseerrAPIError as e:
            raise CommandError(f'Overseerr API error: {e}')
        except Exception as e:
            raise CommandError(f'Unexpected error during synchronization: {e}')
