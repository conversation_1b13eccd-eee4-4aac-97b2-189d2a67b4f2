import requests
import logging
import async<PERSON>
from datetime import datetime
from urllib.parse import urlparse
from django.conf import settings
from django.utils import timezone
from .models import MediaRequest, TVShow, Season, Episode, MediaQuality

# Import python-overseerr library
try:
    from python_overseerr import OverseerrClient
    from python_overseerr.exceptions import OverseerrError
    PYTHON_OVERSEERR_AVAILABLE = True
except ImportError:
    PYTHON_OVERSEERR_AVAILABLE = False

# Import PlexAPI library
try:
    from plexapi.server import PlexServer
    from plexapi.exceptions import PlexApiException, NotFound
    PLEXAPI_AVAILABLE = True
except ImportError:
    PLEXAPI_AVAILABLE = False

logger = logging.getLogger(__name__)


class OverseerrAPIError(Exception):
    """Custom exception for Overseerr API errors"""
    pass


class PlexAPIError(Exception):
    """Custom exception for Plex API errors"""
    pass


class PlexClient:
    """Client for interacting with Plex API"""

    def __init__(self):
        if not PLEXAPI_AVAILABLE:
            raise PlexAPIError("PlexAPI library is not available")

        self.base_url = settings.PLEX_BASE_URL
        self.token = settings.PLEX_TOKEN

        if not self.token:
            raise PlexAPIError("PLEX_TOKEN is not configured")

        self._server = None

    def _get_server(self):
        """Get Plex server instance"""
        if self._server is None:
            try:
                self._server = PlexServer(self.base_url, self.token)
            except PlexApiException as e:
                logger.error(f"Failed to connect to Plex server: {e}")
                raise PlexAPIError(f"Failed to connect to Plex server: {e}")
        return self._server

    def get_show_by_rating_key(self, rating_key):
        """Get a TV show by its Plex rating key"""
        try:
            server = self._get_server()
            # Use the correct Plex API path for fetching by rating key
            return server.fetchItem(f"/library/metadata/{rating_key}")
        except (PlexApiException, NotFound) as e:
            logger.warning(f"Show with rating key {rating_key} not found in Plex: {e}")
            return None
        except Exception as e:
            logger.error(f"Error fetching show from Plex: {e}")
            raise PlexAPIError(f"Error fetching show from Plex: {e}")

    def check_episode_availability(self, rating_key, season_number, episode_number):
        """Check if a specific episode is available in Plex"""
        try:
            show = self.get_show_by_rating_key(rating_key)
            if not show:
                return False

            # Get the season
            try:
                season = show.season(season_number)
            except NotFound:
                logger.debug(f"Season {season_number} not found for show {rating_key}")
                return False

            # Get the episode
            try:
                episode = season.episode(episode_number)
                return episode is not None
            except NotFound:
                logger.debug(f"Episode {season_number}x{episode_number} not found for show {rating_key}")
                return False

        except Exception as e:
            logger.error(f"Error checking episode availability: {e}")
            return False

    def get_available_episodes_for_season(self, rating_key, season_number):
        """Get list of available episode numbers for a season"""
        try:
            show = self.get_show_by_rating_key(rating_key)
            if not show:
                return []

            try:
                season = show.season(season_number)
                return [ep.index for ep in season.episodes()]
            except NotFound:
                logger.debug(f"Season {season_number} not found for show {rating_key}")
                return []

        except Exception as e:
            logger.error(f"Error getting available episodes: {e}")
            return []

    def get_episode_quality_info(self, rating_key, season_number, episode_number):
        """Get quality information for a specific episode"""
        try:
            show = self.get_show_by_rating_key(rating_key)
            if not show:
                return None

            try:
                season = show.season(season_number)
                episode = season.episode(episode_number)

                if not episode or not episode.media:
                    return None

                # Get the best quality media part
                best_media = None
                best_resolution = 0

                for media in episode.media:
                    if hasattr(media, 'height') and media.height:
                        if media.height > best_resolution:
                            best_resolution = media.height
                            best_media = media

                if not best_media:
                    return None

                # Extract quality information
                quality_info = {
                    'resolution': f"{best_media.width}x{best_media.height}" if hasattr(best_media, 'width') and hasattr(best_media, 'height') else None,
                    'height': best_media.height if hasattr(best_media, 'height') else None,
                    'width': best_media.width if hasattr(best_media, 'width') else None,
                    'video_codec': best_media.videoCodec if hasattr(best_media, 'videoCodec') else None,
                    'audio_codec': best_media.audioCodec if hasattr(best_media, 'audioCodec') else None,
                    'container': best_media.container if hasattr(best_media, 'container') else None,
                    'bitrate': best_media.bitrate if hasattr(best_media, 'bitrate') else None,
                    'file_size': best_media.parts[0].size if best_media.parts and hasattr(best_media.parts[0], 'size') else None,
                }

                # Determine quality level based on resolution
                if quality_info['height']:
                    if quality_info['height'] >= 2160:
                        quality_info['quality_level'] = '4K'
                    elif quality_info['height'] >= 800:
                        quality_info['quality_level'] = '1080p'
                    elif quality_info['height'] >= 720:
                        quality_info['quality_level'] = '720p'
                    elif quality_info['height'] >= 480:
                        quality_info['quality_level'] = '480p'
                    else:
                        quality_info['quality_level'] = 'SD'
                else:
                    quality_info['quality_level'] = 'Unknown'

                return quality_info

            except NotFound:
                logger.debug(f"Episode {season_number}x{episode_number} not found for show {rating_key}")
                return None

        except Exception as e:
            logger.error(f"Error getting episode quality info: {e}")
            return None

    def get_episodes_with_quality_for_season(self, rating_key, season_number):
        """Get episodes with quality information for a season"""
        try:
            show = self.get_show_by_rating_key(rating_key)
            if not show:
                return {}

            try:
                season = show.season(season_number)
                episodes_with_quality = {}

                for episode in season.episodes():
                    quality_info = self.get_episode_quality_info(rating_key, season_number, episode.index)
                    episodes_with_quality[episode.index] = quality_info

                return episodes_with_quality

            except NotFound:
                logger.debug(f"Season {season_number} not found for show {rating_key}")
                return {}

        except Exception as e:
            logger.error(f"Error getting episodes with quality: {e}")
            return {}


class AsyncOverseerrWrapper:
    """Async wrapper for the python-overseerr library"""

    def __init__(self):
        if not PYTHON_OVERSEERR_AVAILABLE:
            raise OverseerrAPIError("python-overseerr library is not available")

        # Parse the base URL to extract host and port
        parsed_url = urlparse(settings.OVERSEERR_BASE_URL)
        self.host = parsed_url.hostname or 'localhost'
        self.port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        self.ssl = parsed_url.scheme == 'https'
        self.api_key = settings.OVERSEERR_API_KEY

        if not self.api_key:
            raise OverseerrAPIError("OVERSEERR_API_KEY is not configured")

    async def _get_client(self):
        """Get an OverseerrClient instance"""
        return OverseerrClient(
            host=self.host,
            port=self.port,
            api_key=self.api_key,
            ssl=self.ssl,
            request_timeout=30
        )

    async def get_requests_async(self, take=50, skip=0, filter_type='tv'):
        """Get media requests from Overseerr (async)"""
        try:
            async with await self._get_client() as client:
                # Get all requests and filter by type if needed
                requests = await client.get_requests()

                # Convert to the format expected by the existing code
                results = []
                for request in requests:
                    if hasattr(request, 'media') and hasattr(request.media, 'media_type'):
                        if filter_type == 'tv' and request.media.media_type.value != 'tv':
                            continue
                        elif filter_type == 'movie' and request.media.media_type.value != 'movie':
                            continue

                    # Convert the request object to a dict format
                    request_dict = {
                        'id': request.id,
                        'type': request.media.media_type.value if hasattr(request, 'media') and hasattr(request.media, 'media_type') else 'tv',
                        'status': request.status.value if hasattr(request.status, 'value') else request.status,
                        'createdAt': request.created_at.isoformat() if hasattr(request, 'created_at') else None,
                        'requestedBy': {
                            'displayName': request.requested_by.display_name if hasattr(request, 'requested_by') else 'Unknown'
                        },
                        'media': {
                            'tmdbId': request.media.tmdb_id if hasattr(request, 'media') else None
                        }
                    }
                    results.append(request_dict)

                # Apply pagination
                start_idx = skip
                end_idx = skip + take
                paginated_results = results[start_idx:end_idx]

                return {'results': paginated_results}

        except OverseerrError as e:
            logger.error(f"python-overseerr API error: {e}")
            raise OverseerrAPIError(f"API request failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in async wrapper: {e}")
            raise OverseerrAPIError(f"Unexpected error: {e}")

    async def get_tv_show_details_async(self, tv_id):
        """Get detailed information about a TV show (async)"""
        try:
            async with await self._get_client() as client:
                # Make a direct API call to get raw data instead of using the typed response
                # This avoids the Season model validation issues
                import aiohttp

                url = f"{'https' if self.ssl else 'http'}://{self.host}:{self.port}/api/v1/tv/{tv_id}"
                headers = {'X-Api-Key': self.api_key}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, headers=headers, timeout=30) as response:
                        response.raise_for_status()
                        tv_details = await response.json()

                # Get availability information from mediaInfo and Plex
                media_info = tv_details.get('mediaInfo', {})
                plex_rating_key = media_info.get('ratingKey')

                # Initialize Plex client if available
                plex_client = None
                if PLEXAPI_AVAILABLE and plex_rating_key:
                    try:
                        plex_client = PlexClient()
                    except PlexAPIError as e:
                        logger.warning(f"Failed to initialize Plex client: {e}")

                season_availability = {}
                episode_availability = {}
                episode_quality_info = {}

                # If we have Plex access, check actual availability and quality
                if plex_client and plex_rating_key:
                    logger.info(f"Checking Plex availability and quality for rating key {plex_rating_key}")
                    for season in tv_details.get('seasons', []):
                        season_num = season.get('seasonNumber', 0)

                        # Get episodes with quality information
                        episodes_with_quality = plex_client.get_episodes_with_quality_for_season(plex_rating_key, season_num)
                        available_episodes = list(episodes_with_quality.keys())

                        episode_availability[season_num] = available_episodes
                        episode_quality_info[season_num] = episodes_with_quality

                        # Season has episodes if any are available
                        season_availability[season_num] = bool(available_episodes)
                else:
                    # Fallback to Overseerr status
                    logger.info("Using Overseerr status for availability (Plex not available)")
                    for season_info in media_info.get('seasons', []):
                        season_num = season_info.get('seasonNumber')
                        status = season_info.get('status', 1)
                        # Map Overseerr status to our availability
                        # 1 = Unknown/Unavailable, 3 = Partially Available, 5 = Available
                        has_episodes = status == 5
                        season_availability[season_num] = has_episodes

                # Convert to the format expected by the existing code
                seasons_with_episodes = []
                for season in tv_details.get('seasons', []):
                    season_number = season.get('seasonNumber', 0)

                    # Fetch episodes for this season
                    episodes = []
                    try:
                        episodes_url = f"{'https' if self.ssl else 'http'}://{self.host}:{self.port}/api/v1/tv/{tv_id}/season/{season_number}"
                        async with aiohttp.ClientSession() as episodes_session:
                            async with episodes_session.get(episodes_url, headers=headers, timeout=30) as episodes_response:
                                if episodes_response.status == 200:
                                    season_details = await episodes_response.json()
                                    episodes = season_details.get('episodes', [])
                    except Exception as e:
                        logger.warning(f"Failed to fetch episodes for season {season_number}: {e}")
                        episodes = []

                    # Add quality information to each episode
                    available_episode_numbers = episode_availability.get(season_number, [])
                    season_quality_info = episode_quality_info.get(season_number, {})

                    for episode in episodes:
                        episode_num = episode.get('episodeNumber')
                        if plex_client and available_episode_numbers:
                            # Add quality information if available
                            if episode_num in season_quality_info and season_quality_info[episode_num]:
                                quality_info = season_quality_info[episode_num]
                                episode['quality_info'] = quality_info
                                episode['quality_level'] = quality_info.get('quality_level', 'Unknown')
                                episode['resolution'] = quality_info.get('resolution')
                                episode['video_codec'] = quality_info.get('video_codec')
                                episode['file_size'] = quality_info.get('file_size')

                    seasons_with_episodes.append({
                        'seasonNumber': season_number,
                        'name': season.get('name', f"Season {season_number}"),
                        'overview': season.get('overview', ''),
                        'posterPath': season.get('posterPath', ''),
                        'airDate': season.get('airDate'),
                        'episodes': episodes,

                    })

                return {
                    'name': tv_details.get('name', ''),
                    'overview': tv_details.get('overview', ''),
                    'posterPath': tv_details.get('posterPath', ''),
                    'backdropPath': tv_details.get('backdropPath', ''),
                    'firstAirDate': tv_details.get('firstAirDate'),
                    'lastAirDate': tv_details.get('lastAirDate'),
                    'status': tv_details.get('status', ''),
                    'seasons': seasons_with_episodes,
                    'mediaInfo': media_info,
                    'plexRatingKey': plex_rating_key
                }

        except OverseerrError as e:
            logger.error(f"python-overseerr API error: {e}")
            raise OverseerrAPIError(f"API request failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in async wrapper: {e}")
            raise OverseerrAPIError(f"Unexpected error: {e}")

    async def search_tv_shows_async(self, query, page=1):
        """Search for TV shows (async)"""
        try:
            # Make a direct API call for search - correct endpoint
            import aiohttp
            from urllib.parse import quote

            # URL encode the query properly
            encoded_query = quote(query)
            url = f"{'https' if self.ssl else 'http'}://{self.host}:{self.port}/api/v1/search?query={encoded_query}&page={page}"
            headers = {'X-Api-Key': self.api_key}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=30) as response:
                    response.raise_for_status()
                    search_data = await response.json()

                    # Filter for TV shows only
                    tv_results = [result for result in search_data.get('results', [])
                                 if result.get('mediaType') == 'tv']

                    return {
                        'results': tv_results,
                        'page': search_data.get('page', page),
                        'totalPages': search_data.get('totalPages', 1),
                        'totalResults': len(tv_results)
                    }

        except Exception as e:
            logger.error(f"Unexpected error in async search: {e}")
            raise OverseerrAPIError(f"Unexpected error: {e}")


class OverseerrAPIClient:
    """Client for interacting with Overseerr API"""

    def __init__(self):
        self.base_url = settings.OVERSEERR_BASE_URL.rstrip('/')
        self.api_key = settings.OVERSEERR_API_KEY

        # Initialize async wrapper if available
        self.async_wrapper = None
        if PYTHON_OVERSEERR_AVAILABLE:
            try:
                self.async_wrapper = AsyncOverseerrWrapper()
                logger.info("Using python-overseerr library for API calls")
            except Exception as e:
                logger.warning(f"Failed to initialize python-overseerr wrapper: {e}")
                self.async_wrapper = None

        # Fallback to requests session
        if not self.async_wrapper:
            logger.info("Using requests library for API calls (fallback)")
            self.session = requests.Session()
            self.session.headers.update({
                'X-Api-Key': self.api_key,
                'Content-Type': 'application/json',
            })

    def _run_async(self, coro):
        """Run an async coroutine in a sync context"""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(coro)

    def _make_request(self, endpoint, method='GET', params=None, data=None):
        """Make a request to the Overseerr API (fallback method)"""
        url = f"{self.base_url}/api/v1{endpoint}"

        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Overseerr API request failed: {e}")
            raise OverseerrAPIError(f"API request failed: {e}")

    def get_requests(self, take=50, skip=0, filter_type='tv'):
        """Get media requests from Overseerr"""
        if self.async_wrapper:
            try:
                return self._run_async(
                    self.async_wrapper.get_requests_async(take, skip, filter_type)
                )
            except Exception as e:
                logger.warning(f"Async request failed, falling back to requests: {e}")
                # Fall through to requests fallback

        # Fallback to original requests implementation
        params = {
            'take': take,
            'skip': skip,
            'filter': filter_type
        }
        return self._make_request('/request', params=params)

    def get_tv_show_details(self, tv_id):
        """Get detailed information about a TV show"""
        if self.async_wrapper:
            try:
                return self._run_async(
                    self.async_wrapper.get_tv_show_details_async(tv_id)
                )
            except Exception as e:
                logger.warning(f"Async TV details request failed, falling back to requests: {e}")
                # Fall through to requests fallback

        # Fallback to original requests implementation
        return self._make_request(f'/tv/{tv_id}')

    def get_media_info(self, media_id, media_type='tv'):
        """Get media information"""
        # Ensure session is available for fallback
        if not hasattr(self, 'session') or self.session is None:
            self.session = requests.Session()
            self.session.headers.update({
                'X-Api-Key': self.api_key,
                'Content-Type': 'application/json',
            })

        # This method only uses the fallback for now
        return self._make_request(f'/media/{media_type}/{media_id}')

    def search_tv_shows(self, query, page=1):
        """Search for TV shows"""
        if self.async_wrapper:
            try:
                return self._run_async(
                    self.async_wrapper.search_tv_shows_async(query, page)
                )
            except Exception as e:
                logger.warning(f"Async search failed, falling back to requests: {e}")
                # Fall through to requests fallback

        # Ensure session is available for fallback
        if not hasattr(self, 'session') or self.session is None:
            self.session = requests.Session()
            self.session.headers.update({
                'X-Api-Key': self.api_key,
                'Content-Type': 'application/json',
            })

        # Fallback to original requests implementation with proper URL encoding
        from urllib.parse import quote
        encoded_query = quote(query)
        endpoint = f'/search?query={encoded_query}&page={page}'
        search_data = self._make_request(endpoint)

        # Filter for TV shows only
        tv_results = [result for result in search_data.get('results', [])
                     if result.get('mediaType') == 'tv']

        return {
            'results': tv_results,
            'page': search_data.get('page', page),
            'totalPages': search_data.get('totalPages', 1),
            'totalResults': len(tv_results)
        }

    def request_tv_show(self, tmdb_id, seasons=None):
        """Request a TV show to be added"""
        if not hasattr(self, 'session') or self.session is None:
            self.session = requests.Session()
            self.session.headers.update({
                'X-Api-Key': self.api_key,
                'Content-Type': 'application/json',
            })

        data = {
            'mediaType': 'tv',
            'mediaId': tmdb_id,
            'seasons': seasons or 'all'
        }

        return self._make_request('/request', method='POST', data=data)


class TVShowService:
    """Service for managing TV shows in our database"""

    def __init__(self):
        self.overseerr_client = OverseerrAPIClient()

    def add_tv_show_from_tmdb(self, tmdb_id, added_by="System"):
        """Add a TV show to our database using TMDB data from Overseerr"""
        try:
            # Get TV show details from Overseerr (which fetches from TMDB)
            tv_details = self.overseerr_client.get_tv_show_details(tmdb_id)

            # Parse dates
            first_air_date = None
            last_air_date = None

            if tv_details.get('firstAirDate'):
                try:
                    first_air_date = timezone.datetime.fromisoformat(
                        tv_details['firstAirDate'].replace('Z', '+00:00')
                    ).date()
                except (ValueError, AttributeError):
                    pass

            if tv_details.get('lastAirDate'):
                try:
                    last_air_date = timezone.datetime.fromisoformat(
                        tv_details['lastAirDate'].replace('Z', '+00:00')
                    ).date()
                except (ValueError, AttributeError):
                    pass

            # Create or update TV show
            tv_show, created = TVShow.objects.update_or_create(
                tmdb_id=tmdb_id,
                defaults={
                    'title': tv_details.get('name', '') or '',
                    'overview': tv_details.get('overview', '') or '',
                    'poster_path': tv_details.get('posterPath') or '',
                    'backdrop_path': tv_details.get('backdropPath') or '',
                    'first_air_date': first_air_date,
                    'last_air_date': last_air_date,
                    'status': tv_details.get('status', '') or '',
                    'plex_rating_key': tv_details.get('plexRatingKey', '') or '',
                    'added_by': added_by,
                    'added_at': timezone.now(),
                }
            )

            if created:
                logger.info(f"Created new TV show: {tv_show.title}")
            else:
                logger.info(f"Updated TV show: {tv_show.title}")

            # Sync seasons and episodes with Plex data
            self._sync_tv_show_content(tv_show, tv_details)

            return tv_show

        except Exception as e:
            logger.error(f"Failed to add TV show {tmdb_id}: {e}")
            raise

    def sync_plex_data_for_all_shows(self):
        """Sync Plex availability and quality data for all shows in our database"""
        logger.info("Starting Plex data sync for all TV shows")

        synced_count = 0
        for tv_show in TVShow.objects.all():
            try:
                # Always try to sync - the method will handle missing rating keys
                self._sync_plex_data_for_show(tv_show)
                synced_count += 1
            except Exception as e:
                logger.error(f"Failed to sync Plex data for {tv_show.title}: {e}")
                continue

        logger.info(f"Successfully synced Plex data for {synced_count} TV shows")

    def delete_tv_show(self, show_id, deleted_by="System"):
        """Delete a TV show from our database"""
        try:
            tv_show = TVShow.objects.get(id=show_id)
            show_title = tv_show.title

            # Log the deletion
            logger.info(f"Deleting TV show: {show_title} (ID: {show_id}) by {deleted_by}")

            # Delete the show (this will cascade to seasons and episodes)
            tv_show.delete()

            logger.info(f"Successfully deleted TV show: {show_title}")
            return True

        except TVShow.DoesNotExist:
            logger.warning(f"TV show with ID {show_id} not found")
            return False
        except Exception as e:
            logger.error(f"Failed to delete TV show {show_id}: {e}")
            raise

    def _sync_plex_data_for_show(self, tv_show):
        """Sync Plex data for a specific show"""
        # Get fresh data from Overseerr/Plex
        tv_details = self.overseerr_client.get_tv_show_details(tv_show.tmdb_id)

        # Update Plex rating key if available (even if show didn't have one before)
        if tv_details.get('plexRatingKey'):
            if tv_show.plex_rating_key != tv_details['plexRatingKey']:
                logger.info(f"Updating Plex rating key for {tv_show.title}: {tv_details['plexRatingKey']}")
                tv_show.plex_rating_key = tv_details['plexRatingKey']
                tv_show.save()

        # Only sync detailed content if we have a rating key
        if tv_show.plex_rating_key:
            # Sync seasons and episodes with Plex data
            self._sync_tv_show_content(tv_show, tv_details)
        else:
            logger.debug(f"No Plex rating key available for {tv_show.title}, skipping detailed sync")

    def _sync_tv_show_content(self, tv_show, tv_details):
        """Sync seasons and episodes for a TV show"""
        # Sync seasons and episodes
        for season_data in tv_details.get('seasons', []):
            season_number = season_data.get('seasonNumber', 0)

            # Parse air date
            air_date = None
            if season_data.get('airDate'):
                try:
                    air_date = timezone.datetime.fromisoformat(
                        season_data['airDate'].replace('Z', '+00:00')
                    ).date()
                except (ValueError, AttributeError):
                    pass

            # Create or update season
            season, created = Season.objects.update_or_create(
                tv_show=tv_show,
                season_number=season_number,
                defaults={
                    'name': season_data.get('name', f'Season {season_number}'),
                    'overview': season_data.get('overview', '') or '',
                    'poster_path': season_data.get('posterPath') or '',
                    'air_date': air_date,
                }
            )

            if created:
                logger.info(f"Created season {season_number} for {tv_show.title}")

            # Sync episodes for this season
            for episode_data in season_data.get('episodes', []):
                episode_number = episode_data.get('episodeNumber')
                if episode_number is None:
                    continue

                # Parse air date
                episode_air_date = None
                if episode_data.get('airDate'):
                    try:
                        episode_air_date = timezone.datetime.fromisoformat(
                            episode_data['airDate'].replace('Z', '+00:00')
                        ).date()
                    except (ValueError, AttributeError):
                        pass

                # Handle quality information
                quality_obj = None
                if episode_data.get('quality_info'):
                    quality_info = episode_data['quality_info']
                    quality_level = quality_info.get('quality_level', 'Unknown')
                    video_codec = quality_info.get('video_codec', 'Unknown')

                    # Determine source based on codec and other factors
                    source = 'Unknown'
                    if video_codec:
                        if 'x265' in video_codec.lower() or 'hevc' in video_codec.lower():
                            source = 'WEB-DL'
                        elif 'x264' in video_codec.lower():
                            source = 'WEB-DL'
                        else:
                            source = 'Unknown'

                    # Create or get MediaQuality object
                    try:
                        quality_obj, _ = MediaQuality.objects.get_or_create(
                            resolution=quality_level,
                            source=source,
                            codec=video_codec or 'Unknown',
                            defaults={}
                        )
                    except Exception as e:
                        logger.warning(f"Failed to create quality object: {e}")
                        quality_obj = None

                # Create or update episode
                episode, created = Episode.objects.update_or_create(
                    season=season,
                    episode_number=episode_number,
                    defaults={
                        'name': episode_data.get('name', f'Episode {episode_number}') or f'Episode {episode_number}',
                        'overview': episode_data.get('overview', '') or '',
                        'still_path': episode_data.get('stillPath') or '',
                        'air_date': episode_air_date,
                        'runtime': episode_data.get('runtime'),
                        'quality': quality_obj,
                    }
                )

                if created:
                    logger.debug(f"Created episode {season_number}x{episode_number} for {tv_show.title}")


class OverseerrDataSyncService:
    """Service for synchronizing data from Overseerr API to local database"""

    def __init__(self):
        self.client = OverseerrAPIClient()

    def sync_all_tv_requests(self):
        """Legacy method - now syncs Plex data for existing shows"""
        logger.info("Starting Plex data sync for existing TV shows")

        tv_service = TVShowService()
        tv_service.sync_plex_data_for_all_shows()

    def _sync_media_request(self, request_data):
        """Sync a single media request"""
        overseerr_id = request_data.get('id')

        # Map Overseerr status to our status
        status_mapping = {
            1: 'pending',
            2: 'approved',
            3: 'declined',
            5: 'available'
        }

        status = status_mapping.get(request_data.get('status'), 'pending')

        # Parse requested date
        requested_at = None
        if request_data.get('createdAt'):
            dt_str = request_data['createdAt'].replace('Z', '+00:00')
            dt = datetime.fromisoformat(dt_str)
            # Only make aware if the datetime is naive
            if dt.tzinfo is None:
                requested_at = timezone.make_aware(dt)
            else:
                requested_at = dt

        media_request, created = MediaRequest.objects.update_or_create(
            overseerr_id=overseerr_id,
            defaults={
                'media_type': 'tv' if request_data.get('type') == 'tv' else 'movie',
                'status': status,
                'requested_by': request_data.get('requestedBy', {}).get('displayName', 'Unknown'),
                'requested_at': requested_at or timezone.now(),
            }
        )

        if created:
            logger.info(f"Created new media request: {overseerr_id}")
        else:
            logger.info(f"Updated media request: {overseerr_id}")

        return media_request

    def _sync_tv_show(self, request_data):
        """Sync TV show details"""
        media_data = request_data.get('media', {})
        tmdb_id = media_data.get('tmdbId')

        if not tmdb_id:
            logger.warning(f"No TMDB ID found for request {request_data.get('id')}")
            return

        try:
            # Get detailed TV show information
            tv_details = self.client.get_tv_show_details(tmdb_id)

            # Parse dates
            first_air_date = None
            last_air_date = None

            if tv_details.get('firstAirDate'):
                first_air_date = datetime.strptime(tv_details['firstAirDate'], '%Y-%m-%d').date()

            if tv_details.get('lastAirDate'):
                last_air_date = datetime.strptime(tv_details['lastAirDate'], '%Y-%m-%d').date()

            # Get or create the media request
            media_request = MediaRequest.objects.filter(
                overseerr_id=request_data.get('id')
            ).first()

            # Create or update TV show
            tv_show, created = TVShow.objects.update_or_create(
                tmdb_id=tmdb_id,
                defaults={
                    'title': tv_details.get('name', '') or '',
                    'overview': tv_details.get('overview', '') or '',
                    'poster_path': tv_details.get('posterPath') or '',
                    'backdrop_path': tv_details.get('backdropPath') or '',
                    'first_air_date': first_air_date,
                    'last_air_date': last_air_date,
                    'status': tv_details.get('status', '') or '',
                    'plex_rating_key': tv_details.get('plexRatingKey', '') or '',
                    'media_request': media_request,
                }
            )

            if created:
                logger.info(f"Created new TV show: {tv_show.title}")
            else:
                logger.info(f"Updated TV show: {tv_show.title}")

            # Sync seasons
            self._sync_seasons(tv_show, tv_details)

            # Update availability status
            self._update_tv_show_availability(tv_show)

        except Exception as e:
            logger.error(f"Failed to sync TV show {tmdb_id}: {e}")

    def _sync_seasons(self, tv_show, tv_details):
        """Sync seasons for a TV show"""
        seasons_data = tv_details.get('seasons', [])

        for season_data in seasons_data:
            season_number = season_data.get('seasonNumber')

            if season_number is None:
                continue

            # Parse air date
            air_date = None
            if season_data.get('airDate'):
                try:
                    air_date = datetime.strptime(season_data['airDate'], '%Y-%m-%d').date()
                except ValueError:
                    pass

            season, created = Season.objects.update_or_create(
                tv_show=tv_show,
                season_number=season_number,
                defaults={
                    'name': season_data.get('name', f'Season {season_number}'),
                    'overview': season_data.get('overview', '') or '',
                    'poster_path': season_data.get('posterPath') or '',
                    'air_date': air_date,
                }
            )

            if created:
                logger.info(f"Created season {season_number} for {tv_show.title}")

            # Sync episodes for this season
            self._sync_episodes(season, season_data)

    def _sync_episodes(self, season, season_data):
        """Sync episodes for a season"""
        episodes_data = season_data.get('episodes', [])

        for episode_data in episodes_data:
            episode_number = episode_data.get('episodeNumber')

            if episode_number is None:
                continue

            # Parse air date
            air_date = None
            if episode_data.get('airDate'):
                try:
                    air_date = datetime.strptime(episode_data['airDate'], '%Y-%m-%d').date()
                except ValueError:
                    pass

            # Handle quality information
            quality_obj = None
            if episode_data.get('quality_info'):
                quality_info = episode_data['quality_info']
                quality_level = quality_info.get('quality_level', 'Unknown')
                video_codec = quality_info.get('video_codec', 'Unknown')

                # Determine source based on codec and other factors
                source = 'Unknown'
                if video_codec:
                    if 'x265' in video_codec.lower() or 'hevc' in video_codec.lower():
                        source = 'WEB-DL'
                    elif 'x264' in video_codec.lower():
                        source = 'WEB-DL'
                    else:
                        source = 'Unknown'

                # Create or get MediaQuality object
                try:
                    quality_obj, _ = MediaQuality.objects.get_or_create(
                        resolution=quality_level,
                        source=source,
                        codec=video_codec or 'Unknown',
                        defaults={}
                    )
                except Exception as e:
                    logger.warning(f"Failed to create quality object: {e}")
                    quality_obj = None

            episode, created = Episode.objects.update_or_create(
                season=season,
                episode_number=episode_number,
                defaults={
                    'name': episode_data.get('name', f'Episode {episode_number}') or f'Episode {episode_number}',
                    'overview': episode_data.get('overview', '') or '',
                    'still_path': episode_data.get('stillPath') or '',
                    'air_date': air_date,
                    'runtime': episode_data.get('runtime'),
                    'quality': quality_obj,
                }
            )

            if created:
                logger.info(f"Created episode {episode_number} for {season}")


