# Generated by Django 4.2.23 on 2025-07-30 12:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0004_customformat_qualitydefinition_qualityprofile_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DownloadStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('searching', 'Searching'), ('pending', 'Pending'), ('downloading', 'Downloading'), ('completed', 'Completed'), ('failed', 'Failed'), ('monitoring', 'Monitoring')], default='searching', max_length=20)),
                ('torrent_title', models.CharField(blank=True, max_length=500)),
                ('torrent_hash', models.CharField(blank=True, max_length=40)),
                ('magnet_url', models.TextField(blank=True)),
                ('download_url', models.TextField(blank=True)),
                ('real_debrid_id', models.CharField(blank=True, max_length=50)),
                ('real_debrid_links', models.JSONField(blank=True, default=list)),
                ('quality', models.CharField(blank=True, max_length=20)),
                ('indexer', models.CharField(blank=True, max_length=100)),
                ('size_bytes', models.BigIntegerField(blank=True, null=True)),
                ('seeders', models.IntegerField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('last_checked_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('episode', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='download_status', to='media_requests.episode')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
    ]
