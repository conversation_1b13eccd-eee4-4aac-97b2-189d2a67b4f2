# Generated by Django 4.2.23 on 2025-07-29 11:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MediaQuality',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('resolution', models.CharField(help_text='e.g., 1080p, 720p, 4K', max_length=20)),
                ('source', models.Char<PERSON>ield(help_text='e.g., BluRay, WEB-DL, HDTV', max_length=50)),
                ('codec', models.CharField(help_text='e.g., x264, x265, AV1', max_length=20)),
            ],
            options={
                'verbose_name_plural': 'Media Qualities',
                'unique_together': {('resolution', 'source', 'codec')},
            },
        ),
        migrations.CreateModel(
            name='MediaRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overseerr_id', models.IntegerField(help_text='ID from Overseerr API', unique=True)),
                ('media_type', models.CharField(choices=[('tv', 'TV Show'), ('movie', 'Movie')], max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('declined', 'Declined'), ('available', 'Available')], default='pending', max_length=20)),
                ('requested_by', models.CharField(max_length=100)),
                ('requested_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-requested_at'],
            },
        ),
        migrations.CreateModel(
            name='TVShow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tmdb_id', models.IntegerField(help_text='TMDB ID', unique=True)),
                ('title', models.CharField(max_length=200)),
                ('overview', models.TextField(blank=True)),
                ('poster_path', models.CharField(blank=True, max_length=200)),
                ('backdrop_path', models.CharField(blank=True, max_length=200)),
                ('first_air_date', models.DateField(blank=True, null=True)),
                ('last_air_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(blank=True, max_length=50)),
                ('availability_status', models.CharField(choices=[('unavailable', 'Unavailable'), ('partial', 'Partially Available'), ('available', 'Fully Available')], default='unavailable', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('media_request', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='media_requests.mediarequest')),
            ],
            options={
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='Season',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('season_number', models.IntegerField()),
                ('name', models.CharField(blank=True, max_length=200)),
                ('overview', models.TextField(blank=True)),
                ('poster_path', models.CharField(blank=True, max_length=200)),
                ('air_date', models.DateField(blank=True, null=True)),
                ('availability_status', models.CharField(choices=[('unavailable', 'Unavailable'), ('partial', 'Partially Available'), ('available', 'Fully Available')], default='unavailable', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quality', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='media_requests.mediaquality')),
                ('tv_show', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='seasons', to='media_requests.tvshow')),
            ],
            options={
                'ordering': ['season_number'],
                'unique_together': {('tv_show', 'season_number')},
            },
        ),
        migrations.CreateModel(
            name='Episode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('episode_number', models.IntegerField()),
                ('name', models.CharField(blank=True, max_length=200)),
                ('overview', models.TextField(blank=True)),
                ('still_path', models.CharField(blank=True, max_length=200)),
                ('air_date', models.DateField(blank=True, null=True)),
                ('runtime', models.IntegerField(blank=True, help_text='Runtime in minutes', null=True)),
                ('availability_status', models.CharField(choices=[('unavailable', 'Unavailable'), ('available', 'Available')], default='unavailable', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quality', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='media_requests.mediaquality')),
                ('season', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='episodes', to='media_requests.season')),
            ],
            options={
                'ordering': ['episode_number'],
                'unique_together': {('season', 'episode_number')},
            },
        ),
    ]
