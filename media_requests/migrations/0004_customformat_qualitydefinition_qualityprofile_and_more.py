# Generated by Django 4.2.23 on 2025-07-29 15:21

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0003_tvshow_added_at_tvshow_added_by'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomFormat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('include_patterns', models.JSONField(default=list, help_text='Patterns that must be present (regex supported)')),
                ('exclude_patterns', models.JSONField(default=list, help_text='Patterns that must NOT be present (regex supported)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='QualityDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('resolution', models.CharField(max_length=20)),
                ('source', models.CharField(blank=True, max_length=20)),
                ('priority', models.IntegerField(default=0, help_text='Higher number = higher priority')),
                ('min_size_mb', models.IntegerField(default=0, help_text='Minimum file size in MB')),
                ('max_size_mb', models.IntegerField(default=0, help_text='Maximum file size in MB (0 = unlimited)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='QualityProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('allow_upgrades', models.BooleanField(default=True, help_text='Allow upgrading to better quality')),
                ('minimum_custom_format_score', models.IntegerField(default=0, help_text='Minimum score required for downloads')),
                ('upgrade_until_custom_format_score', models.IntegerField(default=10000, help_text='Stop upgrading once this score is reached')),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('upgrade_until_quality', models.ForeignKey(blank=True, help_text='Stop upgrading once this quality is reached', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='upgrade_until_profiles', to='media_requests.qualitydefinition')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='tvshow',
            name='quality_profile',
            field=models.ForeignKey(blank=True, help_text='Quality profile for this show', null=True, on_delete=django.db.models.deletion.SET_NULL, to='media_requests.qualityprofile'),
        ),
        migrations.CreateModel(
            name='QualityProfileItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('allowed', models.BooleanField(default=True, help_text='Is this quality allowed for download')),
                ('order', models.IntegerField(default=0, help_text='Order within the profile (higher = preferred)')),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_items', to='media_requests.qualityprofile')),
                ('quality', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='media_requests.qualitydefinition')),
            ],
            options={
                'ordering': ['-order', 'quality__priority'],
                'unique_together': {('profile', 'quality')},
            },
        ),
        migrations.CreateModel(
            name='CustomFormatProfileScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField(default=0, help_text='Score for this custom format (-10000 to 10000)', validators=[django.core.validators.MinValueValidator(-10000), django.core.validators.MaxValueValidator(10000)])),
                ('custom_format', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='media_requests.customformat')),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_format_scores', to='media_requests.qualityprofile')),
            ],
            options={
                'ordering': ['-score', 'custom_format__name'],
                'unique_together': {('profile', 'custom_format')},
            },
        ),
    ]
