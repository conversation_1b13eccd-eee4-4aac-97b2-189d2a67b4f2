from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class MediaRequestsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'media_requests'

    def ready(self):
        """Called when Django starts up"""
        # Only start scheduler in production/main process
        # Avoid starting in migrations, tests, or management commands
        import sys
        import os

        # Check if this is the main Django process (avoid starting in reloader process)
        if ('runserver' in sys.argv or 'gunicorn' in sys.argv[0]) and os.environ.get('RUN_MAIN') == 'true':
            try:
                from .scheduler import start_scheduler
                start_scheduler()
                logger.info("Overseerr scheduler started with Django application")
            except Exception as e:
                logger.error(f"Failed to start Overseerr scheduler: {e}")
