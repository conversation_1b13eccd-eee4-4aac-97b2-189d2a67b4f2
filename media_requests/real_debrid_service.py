import requests
import logging
import hashlib
import re
from urllib.parse import quote, urljoin, parse_qs, urlparse
from django.conf import settings
from typing import List, Dict, Optional, Tuple
import time
import bencodepy

logger = logging.getLogger(__name__)


class RealDebridAPIError(Exception):
    """Custom exception for Real Debrid API errors"""
    pass


class RealDebridService:
    """Service for interacting with Real Debrid API"""
    
    def __init__(self):
        self.base_url = 'https://api.real-debrid.com/rest/1.0'
        self.api_key = getattr(settings, 'REAL_DEBRID_API_KEY', '')
        
        if not self.api_key:
            raise RealDebridAPIError("REAL_DEBRID_API_KEY is not configured")
        
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
        })
        self.session.timeout = 30
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.25  # 250ms between requests (240 requests per minute max)

    def _rate_limit(self):
        """Ensure we don't exceed API rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make a rate-limited request to the Real Debrid API"""
        self._rate_limit()

        # Ensure endpoint doesn't start with / to avoid urljoin issues
        if endpoint.startswith('/'):
            endpoint = endpoint[1:]

        url = urljoin(self.base_url + '/', endpoint)
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # Handle rate limiting
            if response.status_code == 429:
                logger.warning("Rate limited by Real Debrid API, waiting 60 seconds")
                time.sleep(60)
                response = self.session.request(method, url, **kwargs)
            
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Real Debrid API request failed: {e}")
            raise RealDebridAPIError(f"API request failed: {e}")

    def extract_hash_from_magnet(self, magnet_url: str) -> Optional[str]:
        """Extract the info hash from a magnet URL"""
        try:
            # Parse the magnet URL
            parsed = urlparse(magnet_url)
            if parsed.scheme != 'magnet':
                return None
            
            # Extract query parameters
            params = parse_qs(parsed.query)
            
            # Look for the xt parameter (exact topic)
            xt_values = params.get('xt', [])
            for xt in xt_values:
                if xt.startswith('urn:btih:'):
                    hash_value = xt[9:]  # Remove 'urn:btih:' prefix
                    # Ensure it's 40 characters (SHA1 hash)
                    if len(hash_value) == 40:
                        return hash_value.lower()
                    elif len(hash_value) == 32:
                        # Convert from base32 to hex if needed
                        try:
                            import base64
                            decoded = base64.b32decode(hash_value.upper())
                            return decoded.hex().lower()
                        except:
                            pass
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract hash from magnet URL: {e}")
            return None

    def torrent_file_to_magnet(self, torrent_url: str, torrent_name: str = None) -> Optional[str]:
        """Convert a torrent file URL to a magnet URL"""
        try:
            # Download the torrent file
            response = requests.get(torrent_url, timeout=30)
            response.raise_for_status()

            # Parse the torrent file
            torrent_data = bencodepy.decode(response.content)

            # Extract info hash
            info = torrent_data[b'info']
            info_encoded = bencodepy.encode(info)
            info_hash = hashlib.sha1(info_encoded).hexdigest()

            # Extract name if not provided
            if not torrent_name:
                torrent_name = info.get(b'name', b'').decode('utf-8', errors='ignore')

            # Extract trackers
            trackers = []

            # Single announce URL
            if b'announce' in torrent_data:
                trackers.append(torrent_data[b'announce'].decode('utf-8', errors='ignore'))

            # Multiple announce URLs
            if b'announce-list' in torrent_data:
                for announce_tier in torrent_data[b'announce-list']:
                    for announce_url in announce_tier:
                        tracker_url = announce_url.decode('utf-8', errors='ignore')
                        if tracker_url not in trackers:
                            trackers.append(tracker_url)

            # Build magnet URL
            magnet_url = f"magnet:?xt=urn:btih:{info_hash}"

            if torrent_name:
                magnet_url += f"&dn={quote(torrent_name)}"

            # Add trackers
            for tracker in trackers[:10]:  # Limit to first 10 trackers
                magnet_url += f"&tr={quote(tracker)}"

            logger.info(f"Successfully converted torrent file to magnet: {torrent_name}")
            return magnet_url

        except Exception as e:
            logger.error(f"Failed to convert torrent file to magnet URL: {e}")
            return None

    def check_instant_availability(self, torrent_hash: str) -> Dict:
        """Check if a torrent is instantly available (cached) on Real Debrid

        NOTE: This endpoint has been deprecated by Real Debrid as of November 2024.
        This method is kept for compatibility but will always return empty.
        Use try_add_and_check_cached() instead.
        """
        logger.warning("instantAvailability endpoint has been deprecated by Real Debrid. Use try_add_and_check_cached() instead.")
        return {}

    def try_add_and_check_cached(self, torrent_url: str, torrent_name: str = None) -> Tuple[bool, Dict]:
        """Try to add a torrent and check if it's cached based on the response

        This is the new approach since Real Debrid deprecated the instantAvailability endpoint.
        We attempt to add the torrent and check the response to determine if it's cached.

        Args:
            torrent_url: Either a magnet URL or torrent file URL
            torrent_name: Optional name for torrent file conversion

        Returns:
            Tuple of (is_cached, info_dict)
        """
        # Determine if it's a magnet URL or torrent file URL
        if torrent_url.startswith('magnet:'):
            # It's a magnet URL
            torrent_hash = self.extract_hash_from_magnet(torrent_url)
            magnet_url = torrent_url
        else:
            # It's a torrent file URL, convert to magnet
            magnet_url = self.torrent_file_to_magnet(torrent_url, torrent_name)
            if not magnet_url:
                return False, {'error': 'Failed to convert torrent file to magnet URL'}

            torrent_hash = self.extract_hash_from_magnet(magnet_url)

        if not torrent_hash:
            return False, {'error': 'Failed to extract torrent hash'}

        # Try to add the magnet to Real Debrid
        torrent_id = self.add_magnet(magnet_url)
        if not torrent_id:
            return False, {
                'hash': torrent_hash,
                'magnet_url': magnet_url,
                'error': 'Failed to add torrent to Real Debrid'
            }

        # Get torrent info to check status (with retry for 404 errors)
        torrent_info = self.get_torrent_info_with_retry(torrent_id, max_retries=3)
        if not torrent_info:
            # Clean up the failed torrent
            self.delete_torrent(torrent_id)
            return False, {
                'hash': torrent_hash,
                'magnet_url': magnet_url,
                'error': 'Failed to get torrent info after retries'
            }

        status = torrent_info.get('status', '')

        # Check if the torrent is instantly available (cached)
        if status == 'downloaded':
            # Torrent is already downloaded (cached)
            logger.info(f"Torrent is cached and ready: {torrent_name}")
            return True, {
                'hash': torrent_hash,
                'magnet_url': magnet_url,
                'torrent_id': torrent_id,
                'status': status,
                'cached': True
            }
        elif status == 'waiting_files_selection':
            # Torrent is cached but needs file selection
            logger.info(f"Torrent is cached, selecting media files: {torrent_name}")

            # Automatically select media files
            if self.auto_select_media_files(torrent_id):
                logger.info(f"Successfully selected media files for: {torrent_name}")

                # Wait a bit for Real Debrid to process the file selection
                if self.wait_for_file_selection_processing(torrent_id, max_wait_seconds=10):
                    return True, {
                        'hash': torrent_hash,
                        'magnet_url': magnet_url,
                        'torrent_id': torrent_id,
                        'status': 'downloaded',
                        'cached': True
                    }
                else:
                    # File selection processed but torrent is not cached
                    logger.warning(f"Torrent file selection processed but not cached: {torrent_name}")
                    self.delete_torrent(torrent_id)
                    return False, {
                        'hash': torrent_hash,
                        'magnet_url': magnet_url,
                        'status': 'not_cached_after_selection',
                        'cached': False,
                        'error': 'Torrent is not cached after file selection'
                    }
            else:
                logger.error(f"Failed to select media files for: {torrent_name}")
                # Clean up the torrent since file selection failed
                self.delete_torrent(torrent_id)
                return False, {
                    'hash': torrent_hash,
                    'magnet_url': magnet_url,
                    'status': status,
                    'cached': False,
                    'error': 'Failed to select media files from torrent'
                }
        else:
            # Torrent is not cached (downloading, queued, etc.)
            logger.info(f"Torrent is not cached (status: {status}): {torrent_name}")
            # Delete the torrent since we don't want uncached downloads
            self.delete_torrent(torrent_id)
            return False, {
                'hash': torrent_hash,
                'magnet_url': magnet_url,
                'status': status,
                'cached': False,
                'error': f'Torrent is not cached (status: {status})'
            }

    def is_torrent_cached(self, torrent_url: str, torrent_name: str = None) -> Tuple[bool, Dict]:
        """Legacy method for backward compatibility. Uses the new try_add_and_check_cached approach."""
        return self.try_add_and_check_cached(torrent_url, torrent_name)

    def add_magnet(self, magnet_url: str) -> Optional[str]:
        """Add a magnet link to Real Debrid and return the torrent ID"""
        try:
            endpoint = 'torrents/addMagnet'
            data = {'magnet': magnet_url}

            # Real Debrid expects form data, not JSON
            response = self._make_request('POST', endpoint, data=data)

            if response.status_code in [200, 201]:
                result = response.json()
                return result.get('id')
            else:
                logger.error(f"Failed to add magnet: HTTP {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Failed to add magnet to Real Debrid: {e}")
            return None

    def get_torrent_info(self, torrent_id: str) -> Optional[Dict]:
        """Get information about a torrent"""
        try:
            endpoint = f'/torrents/info/{torrent_id}'
            response = self._make_request('GET', endpoint)

            if response.status_code == 200:
                return response.json()
            else:
                return None

        except Exception as e:
            logger.error(f"Failed to get torrent info for {torrent_id}: {e}")
            return None

    def get_torrent_info_with_retry(self, torrent_id: str, max_retries: int = 3) -> Optional[Dict]:
        """Get information about a torrent with retry logic for transient errors"""
        import time

        for attempt in range(max_retries):
            try:
                endpoint = f'/torrents/info/{torrent_id}'
                response = self._make_request('GET', endpoint)

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404:
                    # 404 might be transient - Real Debrid sometimes takes time to process
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 2  # 2, 4, 6 seconds
                        logger.warning(f"Torrent {torrent_id} not found (404), retrying in {wait_time}s (attempt {attempt + 1}/{max_retries})")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"Torrent {torrent_id} not found after {max_retries} attempts")
                        return None
                else:
                    logger.error(f"Failed to get torrent info: HTTP {response.status_code}")
                    return None

            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    logger.warning(f"Error getting torrent info for {torrent_id}, retrying in {wait_time}s: {e}")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Failed to get torrent info for {torrent_id} after {max_retries} attempts: {e}")
                    return None

        return None

    def select_files(self, torrent_id: str, file_ids: List[int] = None) -> bool:
        """Select files from a torrent (or select all if file_ids is None)"""
        try:
            endpoint = f'torrents/selectFiles/{torrent_id}'

            if file_ids:
                files_param = ','.join(map(str, file_ids))
            else:
                files_param = 'all'

            data = {'files': files_param}

            # Use form data for file selection
            response = self._make_request('POST', endpoint, data=data)

            return response.status_code in [200, 204]

        except Exception as e:
            logger.error(f"Failed to select files for torrent {torrent_id}: {e}")
            return False

    def filter_media_files(self, files: List[Dict]) -> List[Dict]:
        """Filter torrent files to only include media files and exclude unwanted files"""
        if not files:
            return []

        # Common video file extensions
        video_extensions = {
            '.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v',
            '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.m2ts', '.mts'
        }

        # Files to exclude (case insensitive)
        exclude_patterns = {
            'sample', 'trailer', 'preview', 'proof', 'rarbg', 'etrg', 'yify',
            'readme', 'nfo', 'sfv', 'srr', 'txt', 'url', 'torrent', 'jpg',
            'jpeg', 'png', 'gif', 'bmp', 'sub', 'srt', 'idx', 'sup'
        }

        # File extensions to exclude
        exclude_extensions = {
            '.nfo', '.txt', '.sfv', '.srr', '.url', '.torrent', '.jpg', '.jpeg',
            '.png', '.gif', '.bmp', '.sub', '.srt', '.idx', '.sup', '.rar',
            '.zip', '.7z', '.tar', '.gz', '.exe', '.msi', '.dmg', '.iso'
        }

        filtered_files = []

        for file in files:
            file_path = file.get('path', '').lower()
            file_name = file_path.split('/')[-1] if '/' in file_path else file_path
            file_size = file.get('bytes', 0)

            # Get file extension
            file_ext = None
            if '.' in file_name:
                file_ext = '.' + file_name.split('.')[-1]

            # Skip if file extension is in exclude list
            if file_ext and file_ext in exclude_extensions:
                logger.debug(f"Excluding file by extension: {file_name}")
                continue

            # Skip if file name contains exclude patterns
            if any(pattern in file_name for pattern in exclude_patterns):
                logger.debug(f"Excluding file by pattern: {file_name}")
                continue

            # Skip very small files (likely samples or metadata)
            if file_size < 50 * 1024 * 1024:  # Less than 50MB
                logger.debug(f"Excluding small file: {file_name} ({file_size} bytes)")
                continue

            # Only include video files
            if file_ext and file_ext in video_extensions:
                logger.info(f"Including media file: {file_name} ({file_size} bytes)")
                filtered_files.append(file)
            else:
                logger.debug(f"Excluding non-video file: {file_name}")

        return filtered_files

    def auto_select_media_files(self, torrent_id: str) -> bool:
        """Automatically select only media files from a torrent"""
        try:
            # Get torrent info to see available files
            torrent_info = self.get_torrent_info(torrent_id)
            if not torrent_info:
                logger.error(f"Could not get torrent info for {torrent_id}")
                return False

            files = torrent_info.get('files', [])
            if not files:
                logger.warning(f"No files found in torrent {torrent_id}")
                return False

            # Filter to only media files
            media_files = self.filter_media_files(files)

            if not media_files:
                logger.warning(f"No media files found in torrent {torrent_id}")
                return False

            # Extract file IDs
            file_ids = [file.get('id') for file in media_files if file.get('id') is not None]

            if not file_ids:
                logger.error(f"No valid file IDs found for media files in torrent {torrent_id}")
                return False

            logger.info(f"Selecting {len(file_ids)} media files from torrent {torrent_id}")

            # Select the filtered files
            return self.select_files(torrent_id, file_ids)

        except Exception as e:
            logger.error(f"Failed to auto-select media files for torrent {torrent_id}: {e}")
            return False

    def wait_for_file_selection_processing(self, torrent_id: str, max_wait_seconds: int = 30) -> bool:
        """Wait for Real Debrid to process file selection and check final status"""
        import time

        start_time = time.time()

        while time.time() - start_time < max_wait_seconds:
            try:
                torrent_info = self.get_torrent_info(torrent_id)
                if not torrent_info:
                    return False

                status = torrent_info.get('status', '')

                if status == 'downloaded':
                    logger.info(f"Torrent {torrent_id} is now ready for download")
                    return True
                elif status in ['downloading', 'queued', 'compressing']:
                    logger.info(f"Torrent {torrent_id} is not cached (status: {status})")
                    return False
                elif status == 'error':
                    logger.error(f"Torrent {torrent_id} has error status")
                    return False

                # Still processing, wait a bit
                time.sleep(2)

            except Exception as e:
                logger.error(f"Error checking torrent status: {e}")
                return False

        logger.warning(f"Timeout waiting for torrent {torrent_id} file selection processing")
        return False

    def delete_torrent(self, torrent_id: str) -> bool:
        """Delete a torrent from Real Debrid"""
        try:
            endpoint = f'/torrents/delete/{torrent_id}'
            response = self._make_request('DELETE', endpoint)
            
            return response.status_code in [200, 204]
            
        except Exception as e:
            logger.error(f"Failed to delete torrent {torrent_id}: {e}")
            return False

    def get_download_links(self, torrent_id: str) -> List[str]:
        """Get download links for a completed torrent"""
        try:
            torrent_info = self.get_torrent_info(torrent_id)
            if not torrent_info:
                return []
            
            return torrent_info.get('links', [])
            
        except Exception as e:
            logger.error(f"Failed to get download links for torrent {torrent_id}: {e}")
            return []

    def test_connection(self) -> bool:
        """Test connection to Real Debrid API"""
        try:
            endpoint = '/user'
            response = self._make_request('GET', endpoint)
            
            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"Successfully connected to Real Debrid API as user: {user_info.get('username', 'Unknown')}")
                return True
            else:
                logger.error(f"Real Debrid API test failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Real Debrid API connection test failed: {e}")
            return False
