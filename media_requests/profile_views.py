from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
import json
import logging

from .models import (
    QualityProfile, QualityDefinition, QualityProfileItem, 
    CustomFormat, CustomFormatProfileScore, TVShow
)

logger = logging.getLogger(__name__)


def profiles_list(request):
    """List all quality profiles"""
    profiles = QualityProfile.objects.filter(is_active=True).prefetch_related(
        'quality_items__quality',
        'custom_format_scores__custom_format'
    )
    
    context = {
        'profiles': profiles,
        'page_title': 'Quality Profiles'
    }
    return render(request, 'media_requests/profiles/list.html', context)


def profile_detail(request, profile_id):
    """Show profile details and allow editing"""
    profile = get_object_or_404(QualityProfile, id=profile_id)
    
    # Get all quality definitions for the form
    all_qualities = QualityDefinition.objects.filter(is_active=True).order_by('-priority')
    
    # Get current profile items
    profile_items = QualityProfileItem.objects.filter(profile=profile).select_related('quality')
    profile_quality_ids = set(item.quality.id for item in profile_items)
    
    # Get all custom formats
    all_custom_formats = CustomFormat.objects.filter(is_active=True)
    
    # Get current custom format scores
    format_scores = CustomFormatProfileScore.objects.filter(profile=profile).select_related('custom_format')
    format_score_dict = {score.custom_format.id: score.score for score in format_scores}
    
    context = {
        'profile': profile,
        'all_qualities': all_qualities,
        'profile_quality_ids': profile_quality_ids,
        'profile_items': profile_items,
        'all_custom_formats': all_custom_formats,
        'format_score_dict': format_score_dict,
        'page_title': f'Profile: {profile.name}'
    }
    return render(request, 'media_requests/profiles/detail.html', context)


@require_http_methods(["POST"])
@csrf_exempt
def profile_update(request, profile_id):
    """Update profile settings via AJAX"""
    try:
        profile = get_object_or_404(QualityProfile, id=profile_id)
        data = json.loads(request.body)
        
        with transaction.atomic():
            # Update basic profile settings
            profile.name = data.get('name', profile.name)
            profile.description = data.get('description', profile.description)
            profile.allow_upgrades = data.get('allow_upgrades', profile.allow_upgrades)
            
            # Update upgrade until quality
            upgrade_until_id = data.get('upgrade_until_quality')
            if upgrade_until_id:
                try:
                    upgrade_quality = QualityDefinition.objects.get(id=upgrade_until_id)
                    profile.upgrade_until_quality = upgrade_quality
                except QualityDefinition.DoesNotExist:
                    profile.upgrade_until_quality = None
            else:
                profile.upgrade_until_quality = None
            
            profile.save()
            
            # Update quality items
            if 'quality_items' in data:
                # Remove existing items
                QualityProfileItem.objects.filter(profile=profile).delete()
                
                # Add new items
                for item_data in data['quality_items']:
                    quality_id = item_data.get('quality_id')
                    allowed = item_data.get('allowed', True)
                    order = item_data.get('order', 0)
                    
                    try:
                        quality = QualityDefinition.objects.get(id=quality_id)
                        QualityProfileItem.objects.create(
                            profile=profile,
                            quality=quality,
                            allowed=allowed,
                            order=order
                        )
                    except QualityDefinition.DoesNotExist:
                        continue
            
            # Update custom format scores
            if 'custom_format_scores' in data:
                # Remove existing scores
                CustomFormatProfileScore.objects.filter(profile=profile).delete()
                
                # Add new scores
                for score_data in data['custom_format_scores']:
                    format_id = score_data.get('format_id')
                    score = score_data.get('score', 0)
                    
                    if score != 0:  # Only save non-zero scores
                        try:
                            custom_format = CustomFormat.objects.get(id=format_id)
                            CustomFormatProfileScore.objects.create(
                                profile=profile,
                                custom_format=custom_format,
                                score=score
                            )
                        except CustomFormat.DoesNotExist:
                            continue
        
        return JsonResponse({
            'success': True,
            'message': f'Profile "{profile.name}" updated successfully'
        })
        
    except Exception as e:
        logger.error(f"Failed to update profile: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def profile_create(request):
    """Create a new quality profile"""
    try:
        data = json.loads(request.body)
        
        with transaction.atomic():
            # Create the profile
            profile = QualityProfile.objects.create(
                name=data.get('name', 'New Profile'),
                description=data.get('description', ''),
                allow_upgrades=data.get('allow_upgrades', True)
            )
            
            # Add default quality items (all HD qualities)
            hd_qualities = QualityDefinition.objects.filter(
                resolution__in=['720p', '1080p'],
                is_active=True
            ).order_by('priority')
            
            for quality in hd_qualities:
                QualityProfileItem.objects.create(
                    profile=profile,
                    quality=quality,
                    allowed=True,
                    order=quality.priority
                )
        
        return JsonResponse({
            'success': True,
            'message': f'Profile "{profile.name}" created successfully',
            'profile_id': profile.id
        })
        
    except Exception as e:
        logger.error(f"Failed to create profile: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def profile_delete(request, profile_id):
    """Delete a quality profile"""
    try:
        profile = get_object_or_404(QualityProfile, id=profile_id)
        
        # Check if profile is in use
        shows_using_profile = TVShow.objects.filter(quality_profile=profile).count()
        if shows_using_profile > 0:
            return JsonResponse({
                'success': False,
                'error': f'Cannot delete profile. It is currently used by {shows_using_profile} show(s).'
            }, status=400)
        
        # Check if it's the default profile
        if profile.is_default:
            return JsonResponse({
                'success': False,
                'error': 'Cannot delete the default profile.'
            }, status=400)
        
        profile_name = profile.name
        profile.delete()
        
        return JsonResponse({
            'success': True,
            'message': f'Profile "{profile_name}" deleted successfully'
        })
        
    except Exception as e:
        logger.error(f"Failed to delete profile: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def assign_profile_to_show(request, show_id):
    """Assign a quality profile to a TV show"""
    try:
        show = get_object_or_404(TVShow, id=show_id)
        data = json.loads(request.body)
        
        profile_id = data.get('profile_id')
        if profile_id:
            profile = get_object_or_404(QualityProfile, id=profile_id)
            show.quality_profile = profile
        else:
            show.quality_profile = None
        
        show.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Profile assigned to "{show.title}" successfully'
        })
        
    except Exception as e:
        logger.error(f"Failed to assign profile to show: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def profile_api_data(request, profile_id):
    """Get profile data as JSON for API calls"""
    try:
        profile = get_object_or_404(QualityProfile, id=profile_id)
        
        # Get quality items
        quality_items = []
        for item in profile.quality_items.select_related('quality'):
            quality_items.append({
                'id': item.quality.id,
                'name': item.quality.name,
                'resolution': item.quality.resolution,
                'source': item.quality.source,
                'priority': item.quality.priority,
                'allowed': item.allowed,
                'order': item.order
            })
        
        # Get custom format scores
        format_scores = []
        for score in profile.custom_format_scores.select_related('custom_format'):
            format_scores.append({
                'id': score.custom_format.id,
                'name': score.custom_format.name,
                'score': score.score
            })
        
        profile_data = {
            'id': profile.id,
            'name': profile.name,
            'description': profile.description,
            'allow_upgrades': profile.allow_upgrades,
            'upgrade_until_quality': {
                'id': profile.upgrade_until_quality.id,
                'name': profile.upgrade_until_quality.name
            } if profile.upgrade_until_quality else None,
            'quality_items': quality_items,
            'custom_format_scores': format_scores,
            'is_default': profile.is_default
        }
        
        return JsonResponse({
            'success': True,
            'profile': profile_data
        })
        
    except Exception as e:
        logger.error(f"Failed to get profile data: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


# Custom Format Views

def custom_formats_list(request):
    """List all custom formats"""
    custom_formats = CustomFormat.objects.all().order_by('name')

    context = {
        'custom_formats': custom_formats,
        'page_title': 'Custom Formats'
    }
    return render(request, 'media_requests/profiles/custom_formats.html', context)


@require_http_methods(["POST"])
@csrf_exempt
def custom_format_create(request):
    """Create a new custom format"""
    try:
        data = json.loads(request.body)

        custom_format = CustomFormat.objects.create(
            name=data.get('name', 'New Format'),
            description=data.get('description', ''),
            include_patterns=data.get('include_patterns', []),
            exclude_patterns=data.get('exclude_patterns', []),
            is_active=data.get('is_active', True)
        )

        return JsonResponse({
            'success': True,
            'message': f'Custom format "{custom_format.name}" created successfully',
            'format_id': custom_format.id
        })

    except Exception as e:
        logger.error(f"Failed to create custom format: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def custom_format_update(request, format_id):
    """Update a custom format"""
    try:
        custom_format = get_object_or_404(CustomFormat, id=format_id)
        data = json.loads(request.body)

        custom_format.name = data.get('name', custom_format.name)
        custom_format.description = data.get('description', custom_format.description)
        custom_format.include_patterns = data.get('include_patterns', custom_format.include_patterns)
        custom_format.exclude_patterns = data.get('exclude_patterns', custom_format.exclude_patterns)
        custom_format.is_active = data.get('is_active', custom_format.is_active)
        custom_format.save()

        return JsonResponse({
            'success': True,
            'message': f'Custom format "{custom_format.name}" updated successfully'
        })

    except Exception as e:
        logger.error(f"Failed to update custom format: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def custom_format_delete(request, format_id):
    """Delete a custom format"""
    try:
        custom_format = get_object_or_404(CustomFormat, id=format_id)

        # Check if format is in use
        profiles_using_format = CustomFormatProfileScore.objects.filter(custom_format=custom_format).count()
        if profiles_using_format > 0:
            return JsonResponse({
                'success': False,
                'error': f'Cannot delete format. It is currently used by {profiles_using_format} profile(s).'
            }, status=400)

        format_name = custom_format.name
        custom_format.delete()

        return JsonResponse({
            'success': True,
            'message': f'Custom format "{format_name}" deleted successfully'
        })

    except Exception as e:
        logger.error(f"Failed to delete custom format: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def custom_format_api_data(request, format_id):
    """Get custom format data as JSON"""
    try:
        custom_format = get_object_or_404(CustomFormat, id=format_id)

        format_data = {
            'id': custom_format.id,
            'name': custom_format.name,
            'description': custom_format.description,
            'include_patterns': custom_format.include_patterns,
            'exclude_patterns': custom_format.exclude_patterns,
            'is_active': custom_format.is_active
        }

        return JsonResponse({
            'success': True,
            'format': format_data
        })

    except Exception as e:
        logger.error(f"Failed to get custom format data: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
