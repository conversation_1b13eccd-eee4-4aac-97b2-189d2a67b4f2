from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, timedelta
from .models import MediaRequest, TVShow, Season, Episode, MediaQuality
from .services import OverseerrDataSyncService, OverseerrAPIError
from unittest.mock import patch, MagicMock


class ModelsTestCase(TestCase):
    """Test cases for the models"""

    def setUp(self):
        self.quality = MediaQuality.objects.create(
            resolution='1080p',
            source='WEB-DL',
            codec='x264'
        )

        self.media_request = MediaRequest.objects.create(
            overseerr_id=1001,
            media_type='tv',
            status='approved',
            requested_by='Test User',
            requested_at=timezone.now()
        )

        self.tv_show = TVShow.objects.create(
            tmdb_id=12345,
            title='Test Show',
            overview='A test show for testing',
            first_air_date=date(2020, 1, 1),
            status='Ended',
            media_request=self.media_request
        )

        self.season = Season.objects.create(
            tv_show=self.tv_show,
            season_number=1,
            name='Season 1',
            air_date=date(2020, 1, 1),
            quality=self.quality
        )

    def test_tv_show_str(self):
        """Test TV show string representation"""
        self.assertEqual(str(self.tv_show), 'Test Show')

    def test_season_str(self):
        """Test season string representation"""
        self.assertEqual(str(self.season), 'Test Show - Season 1')

    def test_episode_creation(self):
        """Test episode creation and relationships"""
        episode = Episode.objects.create(
            season=self.season,
            episode_number=1,
            name='Test Episode',
            overview='A test episode',
            air_date=date(2020, 1, 1),
            runtime=45,
            quality=self.quality
        )

        self.assertEqual(episode.tv_show, self.tv_show)
        self.assertEqual(str(episode), 'Test Show S01E01 - Test Episode')

    def test_season_episode_counts(self):
        """Test season episode counting"""
        # Create episodes with quality
        quality, _ = MediaQuality.objects.get_or_create(
            resolution='1080p',
            source='WEB-DL',
            codec='x264'
        )

        Episode.objects.create(
            season=self.season,
            episode_number=1,
            quality=quality
        )
        Episode.objects.create(
            season=self.season,
            episode_number=2
        )

        self.assertEqual(self.season.get_total_episodes(), 2)
        self.assertEqual(self.season.get_available_episodes(), 1)

    def test_tv_show_statistics(self):
        """Test TV show statistics methods"""
        # Create another season
        season2 = Season.objects.create(
            tv_show=self.tv_show,
            season_number=2,
            name='Season 2'
        )

        # Create episodes with quality (available) and without (unavailable)
        Episode.objects.create(season=self.season, episode_number=1, quality=self.quality)
        Episode.objects.create(season=self.season, episode_number=2)  # No quality = unavailable
        Episode.objects.create(season=season2, episode_number=1, quality=self.quality)

        self.assertEqual(self.tv_show.get_total_seasons(), 2)
        self.assertEqual(self.tv_show.get_total_episodes(), 3)
        self.assertEqual(self.tv_show.get_available_episodes(), 2)


class ViewsTestCase(TestCase):
    """Test cases for the views"""

    def setUp(self):
        self.client = Client()
        self.tv_show = TVShow.objects.create(
            tmdb_id=12345,
            title='Test Show',
            overview='A test show'
        )

        self.season = Season.objects.create(
            tv_show=self.tv_show,
            season_number=1,
            name='Season 1'
        )

        self.episode = Episode.objects.create(
            season=self.season,
            episode_number=1,
            name='Test Episode'
        )

    def test_dashboard_view(self):
        """Test dashboard view"""
        response = self.client.get(reverse('dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Show')
        self.assertContains(response, 'Total Shows')

    def test_dashboard_filtering(self):
        """Test dashboard filtering"""
        # Test availability filter
        response = self.client.get(reverse('dashboard'), {'availability': 'available'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Show')

        # Test search
        response = self.client.get(reverse('dashboard'), {'search': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Show')

        # Test search with no results
        response = self.client.get(reverse('dashboard'), {'search': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'Test Show')

    def test_show_detail_view(self):
        """Test show detail view"""
        response = self.client.get(reverse('show_detail', args=[self.tv_show.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Show')
        self.assertContains(response, 'Season 1')

    def test_show_detail_not_found(self):
        """Test show detail view with non-existent show"""
        response = self.client.get(reverse('show_detail', args=[99999]))
        self.assertEqual(response.status_code, 404)

    def test_season_episodes_ajax(self):
        """Test season episodes AJAX view"""
        response = self.client.get(reverse('season_episodes_ajax', args=[self.season.id]))
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertIn('episodes', data)
        self.assertIn('season', data)
        self.assertEqual(len(data['episodes']), 1)
        self.assertEqual(data['episodes'][0]['name'], 'Test Episode')

    def test_api_status_view(self):
        """Test API status view"""
        response = self.client.get(reverse('api_status'))
        self.assertEqual(response.status_code, 500)  # Should fail without proper API config

        data = response.json()
        self.assertIn('status', data)
        self.assertEqual(data['status'], 'error')


class ServicesTestCase(TestCase):
    """Test cases for the services"""

    @patch('media_requests.services.PYTHON_OVERSEERR_AVAILABLE', False)
    @patch('media_requests.services.requests.Session.request')
    def test_overseerr_api_client_success(self, mock_request):
        """Test successful API client request"""
        from .services import OverseerrAPIClient

        # Mock successful response
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {'results': []}
        mock_request.return_value = mock_response

        client = OverseerrAPIClient()
        result = client.get_requests()

        self.assertEqual(result, {'results': []})
        mock_request.assert_called_once()

    @patch('media_requests.services.PYTHON_OVERSEERR_AVAILABLE', False)
    @patch('media_requests.services.requests.Session.request')
    def test_overseerr_api_client_error(self, mock_request):
        """Test API client error handling"""
        from .services import OverseerrAPIClient, OverseerrAPIError
        import requests

        # Mock failed response
        mock_request.side_effect = requests.exceptions.RequestException('Connection error')

        client = OverseerrAPIClient()

        with self.assertRaises(OverseerrAPIError):
            client.get_requests()

    @patch('media_requests.services.OverseerrAPIClient')
    def test_sync_service_success(self, mock_client_class):
        """Test successful data sync"""
        from .services import OverseerrDataSyncService

        # Mock API client
        mock_client = MagicMock()
        mock_client.get_requests.return_value = {
            'results': [{
                'id': 1001,
                'type': 'tv',
                'status': 2,
                'createdAt': '2023-01-01T00:00:00.000Z',
                'requestedBy': {'displayName': 'Test User'},
                'media': {'tmdbId': 12345}
            }]
        }
        mock_client.get_tv_show_details.return_value = {
            'name': 'Test Show',
            'overview': 'A test show',
            'posterPath': '/test.jpg',
            'firstAirDate': '2020-01-01',
            'status': 'Ended',
            'seasons': []
        }
        mock_client_class.return_value = mock_client

        sync_service = OverseerrDataSyncService()
        sync_service.sync_all_tv_requests()

        # Check that data was created
        self.assertTrue(MediaRequest.objects.filter(overseerr_id=1001).exists())
        self.assertTrue(TVShow.objects.filter(tmdb_id=12345).exists())


class IntegrationTestCase(TestCase):
    """Integration tests"""

    def test_full_workflow(self):
        """Test the complete workflow from model creation to view rendering"""
        # Create test data
        quality = MediaQuality.objects.create(
            resolution='1080p',
            source='WEB-DL',
            codec='x264'
        )

        media_request = MediaRequest.objects.create(
            overseerr_id=1001,
            media_type='tv',
            status='approved',
            requested_by='Integration Test User',
            requested_at=timezone.now()
        )

        tv_show = TVShow.objects.create(
            tmdb_id=12345,
            title='Integration Test Show',
            overview='A show for integration testing',
            first_air_date=date(2020, 1, 1),
            status='Ended',
            media_request=media_request
        )

        season = Season.objects.create(
            tv_show=tv_show,
            season_number=1,
            name='Season 1',
            air_date=date(2020, 1, 1),
            quality=quality
        )

        episode = Episode.objects.create(
            season=season,
            episode_number=1,
            name='Integration Test Episode',
            overview='An episode for integration testing',
            air_date=date(2020, 1, 1),
            runtime=45,

            quality=quality
        )

        # Test dashboard view
        response = self.client.get(reverse('dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Integration Test Show')

        # Test show detail view
        response = self.client.get(reverse('show_detail', args=[tv_show.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Integration Test Show')
        self.assertContains(response, 'Season 1')

        # Test episodes AJAX
        response = self.client.get(reverse('season_episodes_ajax', args=[season.id]))
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(len(data['episodes']), 1)
        self.assertEqual(data['episodes'][0]['name'], 'Integration Test Episode')

        # Test filtering
        response = self.client.get(reverse('dashboard'), {'availability': 'available'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Integration Test Show')

        # Test search
        response = self.client.get(reverse('dashboard'), {'search': 'Integration'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Integration Test Show')
